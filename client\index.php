<?php
/**
 * Client Booking Interface
 * Main entry point for the booking system
 */

// Initialize application
require_once __DIR__ . '/../shared/config.php';
require_once __DIR__ . '/../shared/database.php';
require_once __DIR__ . '/../shared/tenant_manager.php';
require_once __DIR__ . '/../shared/session.php';
require_once __DIR__ . '/../shared/localization.php';
require_once __DIR__ . '/../shared/functions.php';

try {
    // Initialize core systems
    Config::init();
    TenantManager::init();
    SessionManager::start();
    LocalizationManager::init();

    // Get current tenant
    $currentTenantName = TenantManager::getCurrentTenant();
    if (!$currentTenantName) {
        throw new Exception('No tenant found');
    }

    // Get tenant database (this will redirect to demo if tenant doesn't exist)
    $db = TenantManager::getDatabase();

    // Get tenant info from database
    $systemDb = Database::master();
    $tenant = $systemDb->fetchRow(
        "SELECT * FROM tenants WHERE subdomain = :subdomain AND status = 'active'",
        [':subdomain' => $currentTenantName]
    );

    if (!$tenant) {
        throw new Exception('Tenant not found in database');
    }

    // Get language
    $language = LocalizationManager::getCurrentLanguage();

    // Get business settings from tenant database
    $businessName = $db->fetchColumn("SELECT value FROM settings WHERE key = 'business_name'") ?: $tenant['business_name'];
    $businessPhone = $db->fetchColumn("SELECT value FROM settings WHERE key = 'business_phone'") ?: '';
    $businessEmail = $db->fetchColumn("SELECT value FROM settings WHERE key = 'business_email'") ?: $tenant['owner_email'];

} catch (Exception $e) {
    error_log("Client booking initialization failed: " . $e->getMessage());
    $businessName = 'Booking System';
    $businessPhone = '';
    $businessEmail = '';
    $tenant = null;
    $language = 'en';
}
?>

<!DOCTYPE html>
<html lang="<?= $language ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($businessName) ?> - Online Booking</title>
    <link rel="stylesheet" href="assets/style.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="business-name"><?= htmlspecialchars($businessName) ?></h1>
                <p class="business-subtitle">Book your appointment online</p>
            </div>
        </header>

        <!-- Progress Bar -->
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-steps">
                <div class="progress-step active" data-step="1">
                    <div class="step-number">1</div>
                    <div class="step-label">Category</div>
                </div>
                <div class="progress-step" data-step="2">
                    <div class="step-number">2</div>
                    <div class="step-label">Service</div>
                </div>
                <div class="progress-step" data-step="3">
                    <div class="step-number">3</div>
                    <div class="step-label">Date</div>
                </div>
                <div class="progress-step" data-step="4">
                    <div class="step-number">4</div>
                    <div class="step-label">Time</div>
                </div>
                <div class="progress-step" data-step="5">
                    <div class="step-number">5</div>
                    <div class="step-label">Details</div>
                </div>
                <div class="progress-step" data-step="6">
                    <div class="step-number">6</div>
                    <div class="step-label">Verify</div>
                </div>
                <div class="progress-step" data-step="7">
                    <div class="step-number">7</div>
                    <div class="step-label">Confirm</div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Loading Overlay -->
            <div class="loading-overlay" id="loadingOverlay">
                <div class="loading-spinner"></div>
                <p class="loading-text">Loading...</p>
            </div>

            <!-- Error Message -->
            <div class="error-message" id="errorMessage" style="display: none;">
                <i class="fas fa-exclamation-circle"></i>
                <span id="errorText"></span>
                <button class="error-close" onclick="hideError()">×</button>
            </div>

            <!-- Step Content Container -->
            <div class="step-content" id="stepContent">
                <!-- Dynamic content will be loaded here -->
            </div>

            <!-- Navigation -->
            <div class="navigation">
                <button class="btn btn-secondary" id="prevBtn" onclick="previousStep()" style="display: none;">
                    <i class="fas fa-arrow-left"></i>
                    Previous
                </button>
                <button class="btn btn-primary" id="nextBtn" onclick="nextStep()" style="display: none;">
                    Next
                    <i class="fas fa-arrow-right"></i>
                </button>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <?php if ($businessPhone): ?>
                    <div class="footer-contact">
                        <i class="fas fa-phone"></i>
                        <a href="tel:<?= htmlspecialchars($businessPhone) ?>"><?= htmlspecialchars($businessPhone) ?></a>
                    </div>
                <?php endif; ?>
                <?php if ($businessEmail): ?>
                    <div class="footer-contact">
                        <i class="fas fa-envelope"></i>
                        <a href="mailto:<?= htmlspecialchars($businessEmail) ?>"><?= htmlspecialchars($businessEmail) ?></a>
                    </div>
                <?php endif; ?>
            </div>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="js/booking.js?v=<?php echo time(); ?>"></script>
    <script>
        // Initialize booking system
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure BookingSystem is loaded before initializing
            if (typeof BookingSystem !== 'undefined' && BookingSystem.instance) {
                BookingSystem.instance.init();
            } else {
                console.error('BookingSystem not loaded properly');
            }
        });
    </script>
</body>
</html>
