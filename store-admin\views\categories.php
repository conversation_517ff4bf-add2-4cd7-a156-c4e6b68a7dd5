<?php
/**
 * Categories View
 * Manage service categories
 */

// Get search and pagination parameters
$searchParams = AdminHelpers::getSearchParams();
$search = $searchParams['search'];
$pageNum = $searchParams['page_num'];
$perPage = $searchParams['per_page'];

// Build search query - search by name, English name, description, and icon
$searchWhere = AdminHelpers::buildSearchWhere($search, ['name', 'name_en', 'description', 'icon']);

// Get total count for pagination first
$countSql = "SELECT COUNT(*) as total FROM categories {$searchWhere['where']}";
$totalCount = $db->fetchRow($countSql, $searchWhere['params'])['total'];

// Create pagination
$pagination = new Pagination($totalCount, $perPage, $pageNum);

// Get categories with proper SQL pagination
$sql = "SELECT * FROM categories {$searchWhere['where']} ORDER BY sort_order ASC, name ASC LIMIT {$perPage} OFFSET {$pagination->getOffset()}";
$paginatedCategories = $db->fetchAll($sql, $searchWhere['params']);



// Add services to each category
foreach ($paginatedCategories as &$category) {
    $category['services'] = $db->fetchAll(
        "SELECT id, name, price, is_active FROM services WHERE category_id = :category_id ORDER BY name ASC",
        [':category_id' => $category['id']]
    );
}
unset($category); // Important: unset the reference to prevent issues with subsequent foreach loops

// Force browser cache refresh
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

?>

<div class="page-header">
    <h2>Categories Management</h2>
    <div class="page-actions">
        <button class="btn btn-primary" onclick="addItem('categories')">
            <i class="fas fa-plus"></i> Add Category
        </button>
        <button class="btn btn-secondary" onclick="location.reload(true)">
            <i class="fas fa-sync"></i> Refresh
        </button>
    </div>
</div>





<!-- Enhanced Toolbar -->
<div class="toolbar">
    <div class="toolbar-left">
        <div class="search-bar">
            <input type="text" placeholder="Search categories, descriptions..." value="<?php echo htmlspecialchars($search); ?>" id="category-search">
            <i class="fas fa-search search-icon"></i>
        </div>
        <div class="filter-group">
            <span class="filter-label">Sort:</span>
            <select class="form-control" id="sort-filter">
                <option value="sort_order">Sort Order</option>
                <option value="name">Name A-Z</option>
                <option value="services_count">Service Count</option>
                <option value="created_at">Date Created</option>
            </select>
        </div>
        <div class="filter-group">
            <span class="filter-label">Services:</span>
            <select class="form-control" id="services-filter">
                <option value="">All Categories</option>
                <option value="with-services">With Services</option>
                <option value="empty">Empty Categories</option>
            </select>
        </div>
    </div>
    <div class="toolbar-right">
        <div class="view-toggle">
            <button class="active" data-view="cards">
                <i class="fas fa-th-large"></i> Cards
            </button>
            <button data-view="table">
                <i class="fas fa-list"></i> Table
            </button>
        </div>
        <button class="btn btn-secondary" onclick="reorderCategories()">
            <i class="fas fa-sort"></i> Reorder
        </button>
        <button class="btn btn-primary" onclick="addCategory()">
            <i class="fas fa-plus"></i> Add Category
        </button>
    </div>
</div>

<!-- Bulk Actions -->
<div class="bulk-actions" id="bulk-actions">
    <span class="bulk-actions-text">
        <span id="selected-count">0</span> categories selected
    </span>
    <div class="bulk-actions-buttons">
        <button class="btn btn-secondary btn-sm" onclick="bulkReorder()">
            <i class="fas fa-sort"></i> Reorder
        </button>
        <button class="btn btn-secondary btn-sm" onclick="bulkUpdateColors()">
            <i class="fas fa-palette"></i> Update Colors
        </button>
        <button class="btn btn-danger btn-sm" onclick="bulkDelete()">
            <i class="fas fa-trash"></i> Delete
        </button>
    </div>
</div>

<!-- Enhanced Categories Grid -->
<div class="entity-grid" id="categories-grid">
    <?php if (empty($paginatedCategories)): ?>
        <div class="empty-state">
            <i class="fas fa-tags fa-4x text-muted"></i>
            <h3>No categories found</h3>
            <p>Start by adding your first category or adjust your search criteria.</p>
            <button class="btn btn-primary btn-lg" onclick="addCategory()">
                <i class="fas fa-plus"></i> Add First Category
            </button>
        </div>
    <?php else: ?>
        <?php foreach ($paginatedCategories as $categoryIndex => $category): ?>
            <?php
            $serviceCount = count($category['services']);
            $categoryColor = $category['color'] ?: '#' . substr(md5($category['name']), 0, 6);
            ?>
            <div class="entity-card category-card" data-id="<?php echo $category['id']; ?>">
                <div class="entity-card-header">
                    <input type="checkbox" class="entity-select" value="<?php echo $category['id']; ?>">
                    <div class="entity-status-indicator <?php echo $category['is_active'] ? 'active' : 'inactive'; ?>"></div>
                    <div class="category-icon" style="background-color: <?php echo $categoryColor; ?>; width: 48px; height: 48px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 20px; margin: 0 auto 12px;">
                        <?php if ($category['icon']): ?>
                            <i class="<?php echo htmlspecialchars($category['icon']); ?>"></i>
                        <?php else: ?>
                            <i class="fas fa-tag"></i>
                        <?php endif; ?>
                    </div>
                    <h3 class="entity-title"><?php echo htmlspecialchars($category['name']); ?></h3>
                    <div class="entity-subtitle">
                        <?php if ($category['name_en']): ?>
                            <?php echo htmlspecialchars($category['name_en']); ?>
                        <?php else: ?>
                            Category
                        <?php endif; ?>
                    </div>
                </div>

                <div class="entity-card-body">
                    <?php if ($category['description']): ?>
                        <div class="entity-info">
                            <div class="info-row">
                                <span class="info-label">
                                    <i class="fas fa-info-circle"></i> Description
                                </span>
                                <span class="info-value">
                                    <?php echo htmlspecialchars(substr($category['description'], 0, 60)); ?><?php echo strlen($category['description']) > 60 ? '...' : ''; ?>
                                </span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="entity-stats">
                        <div class="stat-item">
                            <div class="stat-value"><?php echo $serviceCount; ?></div>
                            <div class="stat-label">Services</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value"><?php echo $category['sort_order'] ?? 0; ?></div>
                            <div class="stat-label">Sort Order</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">
                                <div style="width: 20px; height: 20px; background-color: <?php echo $categoryColor; ?>; border-radius: 4px; border: 1px solid var(--border-color);"></div>
                            </div>
                            <div class="stat-label">Color</div>
                        </div>
                    </div>

                    <?php if (!empty($category['services'])): ?>
                        <div class="category-services">
                            <div class="services-header">
                                <span class="services-label">Services (<?php echo $serviceCount; ?>)</span>
                            </div>
                            <div class="services-list">
                                <?php foreach (array_slice($category['services'], 0, 3) as $service): ?>
                                    <div class="service-item">
                                        <span class="service-name"><?php echo htmlspecialchars($service['name']); ?></span>
                                        <span class="service-price">€<?php echo number_format($service['price'], 0); ?></span>
                                    </div>
                                <?php endforeach; ?>
                                <?php if ($serviceCount > 3): ?>
                                    <div class="service-item more">
                                        <span class="service-name">+<?php echo $serviceCount - 3; ?> more services</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="entity-actions">
                        <button class="btn btn-sm btn-outline" onclick="viewCategory('<?php echo $category['id']; ?>')">
                            <i class="fas fa-eye"></i> View
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editCategory('<?php echo $category['id']; ?>')">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="btn btn-sm btn-success" onclick="manageServices('<?php echo $category['id']; ?>')">
                            <i class="fas fa-cog"></i> Services
                        </button>
                    </div>
                </div>

                <div class="entity-footer">
                    Created: <?php echo date('M j, Y', strtotime($category['created_at'])); ?>
                    <?php if (!$category['is_active']): ?>
                        <span class="status-inactive">• Inactive</span>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; ?>
            <div class="card" data-category-id="<?php echo $category['id']; ?>">
                <div class="card-header">
                    <div class="card-title-section">
                        <h3 class="card-title" style="border-left-color: <?php echo htmlspecialchars($category['color'] ?: '#3498db'); ?>">
                            <?php if ($category['icon']): ?>
                                <i class="<?php echo htmlspecialchars($category['icon']); ?>"
                                   style="color: <?php echo htmlspecialchars($category['color'] ?: '#333'); ?>"></i>
                            <?php endif; ?>
                            <?php echo htmlspecialchars($category['name']); ?>
                        </h3>
                        <?php echo AdminHelpers::renderStatusBadge($category['is_active'] ? 'active' : 'inactive'); ?>
                    </div>
                    
                    <?php echo AdminHelpers::renderActionButtonsWithCheck($db, 'categories', $category['id']); ?>
                </div>
                
                <div class="card-body">
                    <?php echo AdminHelpers::renderCardField('Description', $category['description'] ?: 'No description'); ?>
                    <?php echo AdminHelpers::renderCardField('Sort Order', $category['sort_order']); ?>

                    <?php if (!empty($category['services'])): ?>
                        <div class="card-field">
                            <div class="accordion-header" data-target="services-<?php echo $category['id']; ?>-<?php echo $categoryIndex; ?>">
                                <span class="field-label">Services:</span>
                                <div class="accordion-controls">
                                    <span class="accordion-count"><?php echo count($category['services']); ?></span>
                                    <i class="fas fa-chevron-down accordion-arrow"></i>
                                </div>
                            </div>
                            <div class="accordion-content collapsed" id="services-<?php echo $category['id']; ?>-<?php echo $categoryIndex; ?>">
                                <div class="category-services-grid">
                                    <?php foreach ($category['services'] as $service): ?>
                                        <div class="category-service-item">
                                            <span class="service-name"><?php echo htmlspecialchars($service['name']); ?></span>
                                            <span class="service-price"><?php echo number_format($service['price'], 2); ?>€</span>
                                            <?php if (!$service['is_active']): ?>
                                                <span class="service-inactive">Inactive</span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="card-date">
                        <?php echo date('j/n/Y', strtotime($category['created_at'])); ?>
                    </div>
                </div>
            </div>

        <!-- Add Category Card -->
        <div class="entity-card add-card" onclick="addCategory()">
            <div class="entity-card-body" style="display: flex; align-items: center; justify-content: center; min-height: 200px; flex-direction: column; cursor: pointer;">
                <i class="fas fa-plus fa-3x text-muted" style="margin-bottom: 16px;"></i>
                <h3 style="color: var(--text-muted); margin: 0;">Add New Category</h3>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if ($pagination->getTotalPages() > 1): ?>
    <div class="pagination-container">
        <?php echo $pagination->render(); ?>
    </div>
<?php endif; ?>

<style>
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.page-actions {
    display: flex;
    gap: 10px;
}

.card-title-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-title i {
    font-size: 1.2rem;
}

.pagination-container {
    margin-top: 30px;
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .page-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
}
</style>
