<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Store Admin - Comprehensive Unit Tests</title>
    <link rel="stylesheet" href="store-admin/assets/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-suite {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 400px;
            max-height: 80vh;
            overflow-y: auto;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-xl);
            z-index: 9999;
        }
        
        .test-header {
            padding: 20px;
            background: var(--primary-color);
            color: white;
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        }
        
        .test-content {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .test-category {
            margin-bottom: 20px;
        }
        
        .test-category-title {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .test-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 6px;
            font-size: 13px;
            padding: 4px 0;
        }
        
        .test-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            flex-shrink: 0;
        }
        
        .test-status.pass { background: #22c55e; }
        .test-status.fail { background: #ef4444; }
        .test-status.pending { background: #f59e0b; }
        .test-status.running { background: #3b82f6; animation: pulse 1s infinite; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .test-controls {
            padding: 15px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            gap: 10px;
        }
        
        .test-summary {
            margin-top: 15px;
            padding: 10px;
            background: var(--light-color);
            border-radius: var(--border-radius);
            font-size: 12px;
        }
        
        .test-log {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 400px;
            height: 200px;
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            padding: 10px;
            border-radius: var(--border-radius);
            overflow-y: auto;
            display: none;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- Test Suite Panel -->
    <div class="test-suite">
        <div class="test-header">
            <h3 style="margin: 0;">🧪 Store Admin Unit Tests</h3>
            <p style="margin: 5px 0 0 0; opacity: 0.9; font-size: 14px;">Comprehensive Bug Detection</p>
        </div>
        
        <div class="test-content">
            <!-- Core Functions Tests -->
            <div class="test-category">
                <div class="test-category-title">🔧 Core Functions</div>
                <div class="test-item">
                    <div class="test-status pending" id="test-init-ui"></div>
                    <span>initializeEnhancedUI</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-bulk-actions"></div>
                    <span>updateBulkActions</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-sidebar"></div>
                    <span>initializeSidebar</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-tooltips"></div>
                    <span>initializeTooltips</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-view-toggle"></div>
                    <span>initializeViewToggle</span>
                </div>
            </div>
            
            <!-- Modal System Tests -->
            <div class="test-category">
                <div class="test-category-title">📱 Modal System</div>
                <div class="test-item">
                    <div class="test-status pending" id="test-modal-show"></div>
                    <span>showModal function</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-modal-buttons"></div>
                    <span>Modal buttons present</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-modal-close"></div>
                    <span>Modal close functionality</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-modal-submit"></div>
                    <span>Modal submit functionality</span>
                </div>
            </div>
            
            <!-- Form Generation Tests -->
            <div class="test-category">
                <div class="test-category-title">📝 Form Generation</div>
                <div class="test-item">
                    <div class="test-status pending" id="test-customer-form"></div>
                    <span>generateCustomerForm</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-service-form"></div>
                    <span>generateServiceForm</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-employee-form"></div>
                    <span>generateEmployeeForm</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-category-form"></div>
                    <span>generateCategoryForm</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-reservation-form"></div>
                    <span>generateReservationForm</span>
                </div>
            </div>
            
            <!-- Entity Functions Tests -->
            <div class="test-category">
                <div class="test-category-title">👥 Entity Functions</div>
                <div class="test-item">
                    <div class="test-status pending" id="test-add-customer"></div>
                    <span>addCustomer</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-add-service"></div>
                    <span>addService</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-add-employee"></div>
                    <span>addEmployee</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-add-category"></div>
                    <span>addCategory</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-add-reservation"></div>
                    <span>addReservation</span>
                </div>
            </div>
            
            <!-- Bulk Operations Tests -->
            <div class="test-category">
                <div class="test-category-title">📦 Bulk Operations</div>
                <div class="test-item">
                    <div class="test-status pending" id="test-bulk-toggle"></div>
                    <span>bulkToggleStatus</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-bulk-delete"></div>
                    <span>bulkDelete</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-bulk-email"></div>
                    <span>bulkEmail</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-bulk-sms"></div>
                    <span>bulkSMS</span>
                </div>
            </div>
            
            <!-- Data Loading Tests -->
            <div class="test-category">
                <div class="test-category-title">📊 Data Loading</div>
                <div class="test-item">
                    <div class="test-status pending" id="test-load-categories"></div>
                    <span>loadCategoriesIntoSelect</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-load-customers"></div>
                    <span>loadCustomersIntoSelect</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-load-employees"></div>
                    <span>loadEmployeesIntoSelect</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-load-services"></div>
                    <span>loadServicesIntoSelect</span>
                </div>
            </div>
            
            <!-- UI Components Tests -->
            <div class="test-category">
                <div class="test-category-title">🎨 UI Components</div>
                <div class="test-item">
                    <div class="test-status pending" id="test-notifications"></div>
                    <span>showNotification</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-search"></div>
                    <span>performSearch</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-filters"></div>
                    <span>applyFilters</span>
                </div>
                <div class="test-item">
                    <div class="test-status pending" id="test-table-view"></div>
                    <span>generateTableView</span>
                </div>
            </div>
            
            <div class="test-summary" id="test-summary">
                <strong>Test Summary:</strong><br>
                Total: <span id="total-tests">0</span> |
                Passed: <span id="passed-tests">0</span> |
                Failed: <span id="failed-tests">0</span> |
                Pending: <span id="pending-tests">0</span>
            </div>
        </div>
        
        <div class="test-controls">
            <button class="btn btn-primary btn-sm" onclick="runAllTests()">Run All Tests</button>
            <button class="btn btn-secondary btn-sm" onclick="runCoreTests()">Core Only</button>
            <button class="btn btn-info btn-sm" onclick="toggleLog()">Toggle Log</button>
            <button class="btn btn-warning btn-sm" onclick="resetTests()">Reset</button>
        </div>
    </div>
    
    <!-- Test Log -->
    <div class="test-log" id="test-log"></div>
    
    <!-- Test Environment -->
    <div class="admin-container">
        <nav class="sidebar">
            <div class="sidebar-header">
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Test Environment</h1>
            </div>
            <div class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Test</div>
                    <a href="#" class="nav-link active">
                        <i class="fas fa-flask nav-icon"></i>
                        <span class="nav-text">Unit Tests</span>
                    </a>
                </div>
            </div>
        </nav>
        
        <div class="main-content">
            <div class="admin-header">
                <div class="header-left">
                    <h1>🧪 Store Admin Unit Test Environment</h1>
                </div>
                <div class="header-right">
                    <div class="admin-user">
                        <span>Test User</span>
                        <i class="fas fa-user-circle"></i>
                    </div>
                </div>
            </div>
            
            <div class="page-content">
                <!-- Test Toolbar -->
                <div class="toolbar">
                    <div class="toolbar-left">
                        <div class="search-bar">
                            <input type="text" placeholder="Test search..." id="test-search-input">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                    <div class="toolbar-right">
                        <div class="view-toggle">
                            <button class="active" data-view="cards">
                                <i class="fas fa-th-large"></i> Cards
                            </button>
                            <button data-view="table">
                                <i class="fas fa-list"></i> Table
                            </button>
                        </div>
                        <button class="btn btn-primary" onclick="addCustomer()">
                            <i class="fas fa-plus"></i> Add Customer
                        </button>
                    </div>
                </div>
                
                <!-- Bulk Actions -->
                <div class="bulk-actions" id="bulk-actions">
                    <span class="bulk-actions-text">
                        <span id="selected-count">0</span> items selected
                    </span>
                    <div class="bulk-actions-buttons">
                        <button class="btn btn-secondary btn-sm" onclick="bulkToggleStatus()">
                            <i class="fas fa-toggle-on"></i> Toggle Status
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="bulkDelete()">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
                
                <!-- Test Entity Grid -->
                <div class="entity-grid">
                    <div class="entity-card">
                        <div class="entity-card-header">
                            <input type="checkbox" class="entity-select" value="1">
                            <div class="entity-status-indicator active"></div>
                            <h3 class="entity-title">Test Customer 1</h3>
                            <div class="entity-subtitle">VIP Customer</div>
                        </div>
                        <div class="entity-card-body">
                            <div class="entity-info">
                                <div class="info-row">
                                    <span class="info-label">Email</span>
                                    <span class="info-value"><EMAIL></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Phone</span>
                                    <span class="info-value">+30 ************</span>
                                </div>
                            </div>
                            <div class="entity-stats">
                                <div class="stat-item">
                                    <div class="stat-value">15</div>
                                    <div class="stat-label">Visits</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="entity-card">
                        <div class="entity-card-header">
                            <input type="checkbox" class="entity-select" value="2">
                            <div class="entity-status-indicator warning"></div>
                            <h3 class="entity-title">Test Customer 2</h3>
                            <div class="entity-subtitle">Regular Customer</div>
                        </div>
                        <div class="entity-card-body">
                            <div class="entity-info">
                                <div class="info-row">
                                    <span class="info-label">Email</span>
                                    <span class="info-value"><EMAIL></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Phone</span>
                                    <span class="info-value">+30 ************</span>
                                </div>
                            </div>
                            <div class="entity-stats">
                                <div class="stat-item">
                                    <div class="stat-value">5</div>
                                    <div class="stat-label">Visits</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="store-admin/assets/admin.js"></script>
    <script>
        // Test Framework
        class TestRunner {
            constructor() {
                this.tests = [];
                this.results = {
                    total: 0,
                    passed: 0,
                    failed: 0,
                    pending: 0
                };
            }

            addTest(id, name, testFunction) {
                this.tests.push({ id, name, testFunction });
                this.results.total++;
                this.results.pending++;
            }

            async runTest(test) {
                const statusEl = document.getElementById(test.id);
                if (!statusEl) return;

                statusEl.className = 'test-status running';
                this.log(`Running: ${test.name}`);

                try {
                    const result = await test.testFunction();
                    if (result) {
                        statusEl.className = 'test-status pass';
                        this.results.passed++;
                        this.results.pending--;
                        this.log(`✅ PASS: ${test.name}`);
                    } else {
                        statusEl.className = 'test-status fail';
                        this.results.failed++;
                        this.results.pending--;
                        this.log(`❌ FAIL: ${test.name}`);
                    }
                } catch (error) {
                    statusEl.className = 'test-status fail';
                    this.results.failed++;
                    this.results.pending--;
                    this.log(`❌ ERROR: ${test.name} - ${error.message}`);
                }

                this.updateSummary();
            }

            async runAllTests() {
                this.log('🧪 Starting comprehensive test suite...');
                for (const test of this.tests) {
                    await this.runTest(test);
                    await this.delay(100); // Small delay between tests
                }
                this.log(`🏁 Test suite completed. Passed: ${this.results.passed}, Failed: ${this.results.failed}`);
            }

            updateSummary() {
                document.getElementById('total-tests').textContent = this.results.total;
                document.getElementById('passed-tests').textContent = this.results.passed;
                document.getElementById('failed-tests').textContent = this.results.failed;
                document.getElementById('pending-tests').textContent = this.results.pending;
            }

            log(message) {
                const logEl = document.getElementById('test-log');
                const timestamp = new Date().toLocaleTimeString();
                logEl.innerHTML += `[${timestamp}] ${message}\n`;
                logEl.scrollTop = logEl.scrollHeight;
                console.log(message);
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            reset() {
                this.results = { total: this.tests.length, passed: 0, failed: 0, pending: this.tests.length };
                document.querySelectorAll('.test-status').forEach(el => {
                    el.className = 'test-status pending';
                });
                document.getElementById('test-log').innerHTML = '';
                this.updateSummary();
            }
        }

        // Initialize test runner
        const testRunner = new TestRunner();

        // Core Function Tests
        testRunner.addTest('test-init-ui', 'initializeEnhancedUI', () => {
            return typeof initializeEnhancedUI === 'function';
        });

        testRunner.addTest('test-bulk-actions', 'updateBulkActions', () => {
            return typeof window.updateBulkActions === 'function';
        });

        testRunner.addTest('test-sidebar', 'initializeSidebar', () => {
            return typeof initializeSidebar === 'function';
        });

        testRunner.addTest('test-tooltips', 'initializeTooltips', () => {
            return typeof initializeTooltips === 'function';
        });

        testRunner.addTest('test-view-toggle', 'initializeViewToggle', () => {
            return typeof initializeViewToggle === 'function';
        });

        // Modal System Tests
        testRunner.addTest('test-modal-show', 'showModal function', () => {
            if (typeof showModal !== 'function') return false;
            showModal('Test Modal', '<p>Test content</p>');
            const modal = document.getElementById('admin-modal');
            const hasModal = modal && modal.style.display === 'flex';
            if (hasModal) closeModal();
            return hasModal;
        });

        testRunner.addTest('test-modal-buttons', 'Modal buttons present', () => {
            showModal('Test Modal', '<p>Test content</p>');
            const modal = document.getElementById('admin-modal');
            const saveBtn = modal?.querySelector('.modal-save');
            const cancelBtn = modal?.querySelector('.modal-cancel');
            const hasButtons = saveBtn && cancelBtn;
            if (modal) closeModal();
            return hasButtons;
        });

        testRunner.addTest('test-modal-close', 'Modal close functionality', () => {
            if (typeof closeModal !== 'function') return false;
            showModal('Test Modal', '<p>Test content</p>');
            closeModal();
            const modal = document.getElementById('admin-modal');
            return !modal || modal.style.display === 'none';
        });

        testRunner.addTest('test-modal-submit', 'Modal submit functionality', () => {
            return typeof submitModal === 'function';
        });

        // Form Generation Tests
        testRunner.addTest('test-customer-form', 'generateCustomerForm', () => {
            if (typeof generateCustomerForm !== 'function') return false;
            const form = generateCustomerForm();
            return form && form.includes('customer-name') && form.includes('customer-email');
        });

        testRunner.addTest('test-service-form', 'generateServiceForm', () => {
            if (typeof generateServiceForm !== 'function') return false;
            const form = generateServiceForm();
            return form && form.includes('service-name') && form.includes('service-price');
        });

        testRunner.addTest('test-employee-form', 'generateEmployeeForm', () => {
            if (typeof generateEmployeeForm !== 'function') return false;
            const form = generateEmployeeForm();
            return form && form.includes('employee-name') && form.includes('employee-position');
        });

        testRunner.addTest('test-category-form', 'generateCategoryForm', () => {
            if (typeof generateCategoryForm !== 'function') return false;
            const form = generateCategoryForm();
            return form && form.includes('category-name') && form.includes('category-color');
        });

        testRunner.addTest('test-reservation-form', 'generateReservationForm', () => {
            if (typeof generateReservationForm !== 'function') return false;
            const form = generateReservationForm();
            return form && form.includes('reservation-customer') && form.includes('reservation-service');
        });

        // Entity Function Tests
        testRunner.addTest('test-add-customer', 'addCustomer', () => {
            return typeof addCustomer === 'function';
        });

        testRunner.addTest('test-add-service', 'addService', () => {
            return typeof addService === 'function';
        });

        testRunner.addTest('test-add-employee', 'addEmployee', () => {
            return typeof addEmployee === 'function';
        });

        testRunner.addTest('test-add-category', 'addCategory', () => {
            return typeof addCategory === 'function';
        });

        testRunner.addTest('test-add-reservation', 'addReservation', () => {
            return typeof addReservation === 'function';
        });

        // Bulk Operations Tests
        testRunner.addTest('test-bulk-toggle', 'bulkToggleStatus', () => {
            return typeof bulkToggleStatus === 'function';
        });

        testRunner.addTest('test-bulk-delete', 'bulkDelete', () => {
            return typeof bulkDelete === 'function';
        });

        testRunner.addTest('test-bulk-email', 'bulkEmail', () => {
            return typeof bulkEmail === 'function';
        });

        testRunner.addTest('test-bulk-sms', 'bulkSMS', () => {
            return typeof bulkSMS === 'function';
        });

        // Data Loading Tests
        testRunner.addTest('test-load-categories', 'loadCategoriesIntoSelect', () => {
            return typeof loadCategoriesIntoSelect === 'function';
        });

        testRunner.addTest('test-load-customers', 'loadCustomersIntoSelect', () => {
            return typeof loadCustomersIntoSelect === 'function';
        });

        testRunner.addTest('test-load-employees', 'loadEmployeesIntoSelect', () => {
            return typeof loadEmployeesIntoSelect === 'function';
        });

        testRunner.addTest('test-load-services', 'loadServicesIntoSelect', () => {
            return typeof loadServicesIntoSelect === 'function';
        });

        // UI Components Tests
        testRunner.addTest('test-notifications', 'showNotification', () => {
            if (typeof showNotification !== 'function') return false;
            showNotification('Test notification', 'info');
            return document.querySelector('.notification') !== null;
        });

        testRunner.addTest('test-search', 'performSearch', () => {
            return typeof performSearch === 'function';
        });

        testRunner.addTest('test-filters', 'applyFilters', () => {
            return typeof applyFilters === 'function';
        });

        testRunner.addTest('test-table-view', 'generateTableView', () => {
            return typeof generateTableView === 'function';
        });

        // Global test functions
        window.runAllTests = () => testRunner.runAllTests();
        window.runCoreTests = async () => {
            const coreTests = testRunner.tests.filter(test =>
                test.id.includes('init-ui') ||
                test.id.includes('bulk-actions') ||
                test.id.includes('sidebar') ||
                test.id.includes('tooltips') ||
                test.id.includes('view-toggle')
            );
            for (const test of coreTests) {
                await testRunner.runTest(test);
                await testRunner.delay(100);
            }
        };

        window.toggleLog = () => {
            const log = document.getElementById('test-log');
            log.style.display = log.style.display === 'none' ? 'block' : 'none';
        };

        window.resetTests = () => testRunner.reset();

        // Initialize summary
        testRunner.updateSummary();

        // Auto-run tests on load
        document.addEventListener('DOMContentLoaded', function() {
            testRunner.log('🔧 Store Admin Unit Test Environment Loaded');
            testRunner.log('Click "Run All Tests" to start comprehensive testing');
        });
    </script>
</body>
</html>
