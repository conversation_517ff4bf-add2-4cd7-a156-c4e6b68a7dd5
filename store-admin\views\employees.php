<?php
/**
 * Employees View
 * Manage employees
 */

// Get search and pagination parameters
$searchParams = AdminHelpers::getSearchParams();
$search = $searchParams['search'];
$pageNum = $searchParams['page_num'];
$perPage = $searchParams['per_page'];

// Build search query - search by name, English name, email, phone, and position
$searchWhere = AdminHelpers::buildSearchWhere($search, ['name', 'name_en', 'email', 'phone', 'position']);

// Get total count for pagination first
$countSql = "SELECT COUNT(*) as total FROM employees {$searchWhere['where']}";
$totalCount = $db->fetchRow($countSql, $searchWhere['params'])['total'];

// Create pagination
$pagination = new Pagination($totalCount, $perPage, $pageNum);

// Get employees with proper SQL pagination
$sql = "SELECT * FROM employees {$searchWhere['where']} ORDER BY name ASC LIMIT {$perPage} OFFSET {$pagination->getOffset()}";
$paginatedEmployees = $db->fetchAll($sql, $searchWhere['params']);

// Get employee services for each employee (with error handling)
foreach ($paginatedEmployees as &$employee) {
    try {
        $employee['services'] = $db->fetchAll("
            SELECT s.name FROM services s
            JOIN employee_services es ON s.id = es.service_id
            WHERE es.employee_id = :employee_id
            ORDER BY s.name
        ", [':employee_id' => $employee['id']]);
    } catch (Exception $e) {
        // If employee_services table doesn't exist or query fails, set empty array
        $employee['services'] = [];
    }
}
unset($employee); // Important: unset the reference to prevent issues with subsequent foreach loops

?>

<!-- Enhanced Toolbar -->
<div class="toolbar">
    <div class="toolbar-left">
        <div class="search-bar">
            <input type="text" placeholder="Search by name, position, email..." value="<?php echo htmlspecialchars($search); ?>" id="employee-search">
            <i class="fas fa-search search-icon"></i>
        </div>
        <div class="filter-group">
            <span class="filter-label">Status:</span>
            <select class="form-control" id="status-filter">
                <option value="">All Employees</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
            </select>
        </div>
        <div class="filter-group">
            <span class="filter-label">Position:</span>
            <select class="form-control" id="position-filter">
                <option value="">All Positions</option>
                <?php
                $positions = $db->fetchAll("SELECT DISTINCT position FROM employees WHERE position IS NOT NULL AND position != '' ORDER BY position");
                foreach ($positions as $pos): ?>
                    <option value="<?php echo htmlspecialchars($pos['position']); ?>"><?php echo htmlspecialchars($pos['position']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="filter-group">
            <span class="filter-label">Skills:</span>
            <select class="form-control" id="skills-filter">
                <option value="">All Skills</option>
                <option value="1-3">1-3 Services</option>
                <option value="4-6">4-6 Services</option>
                <option value="7+">7+ Services</option>
            </select>
        </div>
    </div>
    <div class="toolbar-right">
        <div class="view-toggle">
            <button class="active" data-view="cards">
                <i class="fas fa-th-large"></i> Cards
            </button>
            <button data-view="table">
                <i class="fas fa-list"></i> Table
            </button>
        </div>
        <button class="btn btn-secondary" onclick="manageSchedules()">
            <i class="fas fa-calendar"></i> Schedules
        </button>
        <button class="btn btn-primary" onclick="addEmployee()">
            <i class="fas fa-plus"></i> Add Employee
        </button>
    </div>
</div>

<!-- Bulk Actions -->
<div class="bulk-actions" id="bulk-actions">
    <span class="bulk-actions-text">
        <span id="selected-count">0</span> employees selected
    </span>
    <div class="bulk-actions-buttons">
        <button class="btn btn-secondary btn-sm" onclick="bulkSchedule()">
            <i class="fas fa-calendar"></i> Schedule
        </button>
        <button class="btn btn-secondary btn-sm" onclick="bulkNotify()">
            <i class="fas fa-bell"></i> Notify
        </button>
        <button class="btn btn-secondary btn-sm" onclick="bulkToggleStatus()">
            <i class="fas fa-toggle-on"></i> Toggle Status
        </button>
        <button class="btn btn-danger btn-sm" onclick="bulkDelete()">
            <i class="fas fa-trash"></i> Delete
        </button>
    </div>
</div>

<!-- Enhanced Employees Grid -->
<div class="entity-grid" id="employees-grid">
    <?php if (empty($paginatedEmployees)): ?>
        <div class="empty-state">
            <i class="fas fa-user-tie fa-4x text-muted"></i>
            <h3>No employees found</h3>
            <p>Start by adding your first employee or adjust your search criteria.</p>
            <button class="btn btn-primary btn-lg" onclick="addEmployee()">
                <i class="fas fa-plus"></i> Add First Employee
            </button>
        </div>
    <?php else: ?>
        <?php foreach ($paginatedEmployees as $employee): ?>
            <?php
            $serviceCount = count($employee['services']);
            $skillLevel = $serviceCount > 6 ? 'expert' : ($serviceCount > 3 ? 'experienced' : 'junior');
            $avatarColor = $employee['color'] ?: '#' . substr(md5($employee['name']), 0, 6);
            ?>
            <div class="entity-card employee-card" data-id="<?php echo $employee['id']; ?>">
                <div class="entity-card-header">
                    <input type="checkbox" class="entity-select" value="<?php echo $employee['id']; ?>">
                    <div class="entity-status-indicator <?php echo $employee['is_active'] ? 'active' : 'inactive'; ?>"></div>
                    <div class="employee-avatar" style="background-color: <?php echo $avatarColor; ?>; width: 48px; height: 48px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 18px; margin: 0 auto 12px;">
                        <?php echo strtoupper(substr($employee['name'], 0, 1)); ?>
                    </div>
                    <h3 class="entity-title"><?php echo htmlspecialchars($employee['name']); ?></h3>
                    <div class="entity-subtitle">
                        <?php echo htmlspecialchars($employee['position'] ?: 'Staff Member'); ?>
                    </div>
                </div>

                <div class="entity-card-body">
                    <div class="entity-info">
                        <?php if (!empty($employee['email'])): ?>
                            <div class="info-row">
                                <span class="info-label">
                                    <i class="fas fa-envelope"></i> Email
                                </span>
                                <a href="mailto:<?php echo htmlspecialchars($employee['email']); ?>" class="info-value contact-link">
                                    <?php echo htmlspecialchars($employee['email']); ?>
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($employee['phone'])): ?>
                            <div class="info-row">
                                <span class="info-label">
                                    <i class="fas fa-phone"></i> Phone
                                </span>
                                <a href="tel:<?php echo htmlspecialchars($employee['phone']); ?>" class="info-value contact-link">
                                    <?php echo htmlspecialchars($employee['phone']); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="entity-stats">
                        <div class="stat-item">
                            <div class="stat-value"><?php echo $serviceCount; ?></div>
                            <div class="stat-label">Services</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">4.8⭐</div>
                            <div class="stat-label">Rating</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value"><?php echo ucfirst($skillLevel); ?></div>
                            <div class="stat-label">Level</div>
                        </div>
                    </div>

                    <?php if (!empty($employee['services'])): ?>
                        <div class="employee-skills">
                            <div class="skills-header">
                                <span class="skills-label">Skills (<?php echo $serviceCount; ?>)</span>
                            </div>
                            <div class="skills-list">
                                <?php foreach (array_slice($employee['services'], 0, 3) as $service): ?>
                                    <span class="skill-badge"><?php echo htmlspecialchars($service['name']); ?></span>
                                <?php endforeach; ?>
                                <?php if ($serviceCount > 3): ?>
                                    <span class="skill-badge more">+<?php echo $serviceCount - 3; ?> more</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="entity-actions">
                        <button class="btn btn-sm btn-outline" onclick="viewEmployee('<?php echo $employee['id']; ?>')">
                            <i class="fas fa-eye"></i> View
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editEmployee('<?php echo $employee['id']; ?>')">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="btn btn-sm btn-success" onclick="manageSchedule('<?php echo $employee['id']; ?>')">
                            <i class="fas fa-calendar"></i> Schedule
                        </button>
                    </div>
                </div>

                <div class="entity-footer">
                    Joined: <?php echo date('M j, Y', strtotime($employee['created_at'])); ?>
                    <?php if (!$employee['is_active']): ?>
                        <span class="status-inactive">• Inactive</span>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; ?>
            <div class="card">
                <div class="card-header">
                    <div class="card-title-section">
                        <h3 class="card-title" style="border-left-color: <?php echo htmlspecialchars($employee['color'] ?: '#3498db'); ?>">
                            <div class="employee-avatar" style="background-color: <?php echo htmlspecialchars($employee['color'] ?: '#3498db'); ?>">
                                <?php echo strtoupper(substr($employee['name'], 0, 1)); ?>
                            </div>
                            <?php echo htmlspecialchars($employee['name']); ?>
                        </h3>
                        <?php echo AdminHelpers::renderStatusBadge($employee['is_active'] ? 'active' : 'inactive'); ?>
                    </div>
                    
                    <?php echo AdminHelpers::renderActionButtonsWithCheck($db, 'employees', $employee['id']); ?>
                </div>
                
                <div class="card-body">
                    <?php echo AdminHelpers::renderCardField('Position', $employee['position']); ?>
                    
                    <?php echo AdminHelpers::formatContactInfo([
                        'email' => $employee['email'],
                        'phone' => $employee['phone']
                    ]); ?>
                    
                    <?php if (!empty($employee['services'])): ?>
                        <div class="card-field">
                            <div class="accordion-header" data-target="services-<?php echo $employee['id']; ?>">
                                <span class="field-label">Services:</span>
                                <div class="accordion-controls">
                                    <span class="accordion-count"><?php echo count($employee['services']); ?></span>
                                    <i class="fas fa-chevron-down accordion-arrow"></i>
                                </div>
                            </div>
                            <div class="accordion-content collapsed" id="services-<?php echo $employee['id']; ?>">
                                <div class="services-grid">
                                    <?php foreach ($employee['services'] as $service): ?>
                                        <span class="service-badge"><?php echo htmlspecialchars($service['name']); ?></span>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php
                    // Parse working hours - handle both old and new formats
                    $workingHours = json_decode($employee['working_hours'], true);
                    if ($workingHours && is_array($workingHours)) {
                        $workingDays = [];
                        $workingDetails = [];

                        foreach ($workingHours as $day => $hours) {
                            if (!empty($hours)) {
                                $dayName = ucfirst($day);

                                if (is_array($hours)) {
                                    // Old format: [['start' => '09:00', 'end' => '17:00']]
                                    if (isset($hours[0]['start']) && isset($hours[0]['end'])) {
                                        $timeRange = $hours[0]['start'] . '-' . $hours[0]['end'];
                                        $workingDays[] = $dayName;
                                        $workingDetails[] = $dayName . ': ' . $timeRange;
                                    }
                                } else {
                                    // New format: '09:00-17:00'
                                    $workingDays[] = $dayName;
                                    $workingDetails[] = $dayName . ': ' . $hours;
                                }
                            }
                        }

                        if (!empty($workingDays)) {
                    ?>
                            <div class="card-field">
                                <div class="accordion-header" data-target="working-<?php echo $employee['id']; ?>">
                                    <span class="field-label">Working Hours:</span>
                                    <div class="accordion-controls">
                                        <span class="accordion-count"><?php echo count($workingDays); ?></span>
                                        <i class="fas fa-chevron-down accordion-arrow"></i>
                                    </div>
                                </div>
                                <div class="accordion-content collapsed" id="working-<?php echo $employee['id']; ?>">
                                    <div class="working-schedule">
                                        <?php foreach ($workingDetails as $detail): ?>
                                            <div class="schedule-item"><?php echo htmlspecialchars($detail); ?></div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                    <?php
                        }
                    }
                    ?>
                    
                    <div class="card-date">
                        <?php echo date('j/n/Y', strtotime($employee['created_at'])); ?>
                    </div>
                </div>
            </div>

        <!-- Add Employee Card -->
        <div class="entity-card add-card" onclick="addEmployee()">
            <div class="entity-card-body" style="display: flex; align-items: center; justify-content: center; min-height: 200px; flex-direction: column; cursor: pointer;">
                <i class="fas fa-plus fa-3x text-muted" style="margin-bottom: 16px;"></i>
                <h3 style="color: var(--text-muted); margin: 0;">Add New Employee</h3>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if ($pagination->getTotalPages() > 1): ?>
    <div class="pagination-container">
        <?php echo $pagination->render(); ?>
    </div>
<?php endif; ?>

<style>
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.page-actions {
    display: flex;
    gap: 10px;
}

.card-title-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.employee-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.services-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
}

.service-badge {
    padding: 2px 6px;
    background: var(--primary-color);
    color: white;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: 500;
}

.pagination-container {
    margin-top: 30px;
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .page-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
}
</style>

<script>
// Employee working hours period management
function addEmployeePeriod(day) {
    const container = document.getElementById(`${day}_employee_periods`);
    if (!container) {
        console.error(`Container not found: ${day}_employee_periods`);
        return;
    }

    const periods = container.querySelectorAll('.time-period');
    const newIndex = periods.length;

    const newPeriod = document.createElement('div');
    newPeriod.className = 'time-period';
    newPeriod.setAttribute('data-period', newIndex);

    newPeriod.innerHTML = `
        <input type="time" name="${day}_start[]" value="17:00" required>
        <span>to</span>
        <input type="time" name="${day}_end[]" value="21:00" required>
        <button type="button" class="btn-remove-period" onclick="removeEmployeePeriod('${day}', ${newIndex})">
            <i class="fas fa-times"></i>
        </button>
    `;

    container.appendChild(newPeriod);
}

function removeEmployeePeriod(day, index) {
    const container = document.getElementById(`${day}_employee_periods`);
    if (!container) {
        console.error(`Container not found: ${day}_employee_periods`);
        return;
    }

    const period = container.querySelector(`[data-period="${index}"]`);
    if (period) {
        period.remove();

        // Reindex remaining periods
        const remainingPeriods = container.querySelectorAll('.time-period');
        remainingPeriods.forEach((period, newIndex) => {
            period.setAttribute('data-period', newIndex);
            const removeBtn = period.querySelector('.btn-remove-period');
            if (removeBtn) {
                removeBtn.setAttribute('onclick', `removeEmployeePeriod('${day}', ${newIndex})`);
            }
        });
    }
}

// Toggle day hours visibility
function toggleDayHours(day) {
    const checkbox = document.querySelector(`input[name="working_days[]"][value="${day}"]`);
    const timesDiv = document.getElementById(`${day}_times`);

    if (checkbox && timesDiv) {
        if (checkbox.checked) {
            timesDiv.style.display = 'block';
        } else {
            timesDiv.style.display = 'none';
        }
    }
}
</script>
