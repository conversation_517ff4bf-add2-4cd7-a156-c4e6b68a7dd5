<?php

/**
 * Dummy Data Generation Interface
 * Generate test data for tenants
 */

require_once __DIR__ . '/config.php';

// Require system authentication
requireSystemAuth();

$message = '';
$messageType = 'info';

// Get all tenants
$tenants = Database::master()->fetchAll("SELECT * FROM tenants ORDER BY business_name");

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $tenantId = $_POST['tenant_id'] ?? '';
    $dataTypes = $_POST['data_types'] ?? [];
    
    if (empty($tenantId)) {
        $message = "Please select a tenant";
        $messageType = 'error';
    } elseif (empty($dataTypes)) {
        $message = "Please select at least one data type to generate";
        $messageType = 'error';
    } else {
        try {
            // Check if DummyData class exists
            if (!file_exists(__DIR__ . '/../shared/DummyData.php')) {
                throw new Exception("DummyData.php file not found");
            }

            require_once __DIR__ . '/../shared/DummyData.php';

            if (!class_exists('DummyData')) {
                throw new Exception("DummyData class not found");
            }

            $dummyData = new DummyData();

            // Get tenant subdomain from ID
            $tenant = Database::master()->fetchRow("SELECT subdomain FROM tenants WHERE id = :id", [':id' => $tenantId]);
            if (!$tenant) {
                throw new Exception("Tenant not found");
            }

            $tenantDb = Database::tenant($tenant['subdomain']);

            $results = [];
            
            // Generate selected data types
            foreach ($dataTypes as $dataType) {
                switch ($dataType) {
                    case 'categories':
                        $count = $dummyData->generateCategories($tenantDb);
                        $results[] = "Generated $count categories";
                        break;
                        
                    case 'services':
                        $count = $dummyData->generateServices($tenantDb);
                        $results[] = "Generated $count services";
                        break;
                        
                    case 'employees':
                        $count = $dummyData->generateEmployees($tenantDb);
                        $results[] = "Generated $count employees";
                        break;
                        
                    case 'customers':
                        $count = $dummyData->generateCustomers($tenantDb);
                        $results[] = "Generated $count customers";
                        break;
                        
                    case 'reservations':
                        $count = $dummyData->generateReservations($tenantDb);
                        $results[] = "Generated $count reservations";
                        break;
                        
                    case 'all':
                        $dummyData->generateAll($tenantDb);
                        $results[] = "Generated complete dataset";
                        break;
                }
            }
            
            $message = "Dummy data generated successfully:<br>" . implode('<br>', $results);
            $messageType = 'success';
            
        } catch (Exception $e) {
            $message = "Error generating dummy data: " . $e->getMessage();
            $messageType = 'error';
            error_log("Dummy data generation error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
        }
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= SYSTEM_NAME ?> - Dummy Data Generator</title>
    <link rel="stylesheet" href="assets/system.css">
</head>
<body>
    <div class="layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1><?= SYSTEM_NAME ?></h1>
                <button class="sidebar-toggle">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <line x1="3" y1="12" x2="21" y2="12"></line>
                        <line x1="3" y1="18" x2="21" y2="18"></line>
                    </svg>
                </button>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <a href="index.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="7" height="7"></rect>
                            <rect x="14" y="3" width="7" height="7"></rect>
                            <rect x="14" y="14" width="7" height="7"></rect>
                            <rect x="3" y="14" width="7" height="7"></rect>
                        </svg>
                        <span class="nav-text">Dashboard</span>
                    </a>
                    <a href="tenant_manager.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                        <span class="nav-text">Tenant Manager</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Management</div>
                    <a href="create_tenant.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="16"></line>
                            <line x1="8" y1="12" x2="16" y2="12"></line>
                        </svg>
                        <span class="nav-text">Create Tenant</span>
                    </a>
                    <a href="dummy_data.php" class="nav-link active">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10,9 9,9 8,9"></polyline>
                        </svg>
                        <span class="nav-text">Dummy Data</span>
                    </a>
                    <a href="clean_db.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 7h16"></path>
                            <path d="M10 11v6"></path>
                            <path d="M14 11v6"></path>
                            <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2l1-12"></path>
                            <path d="M9 7V4a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3"></path>
                        </svg>
                        <span class="nav-text">Database Tools</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <a href="?logout=1" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                            <polyline points="16,17 21,12 16,7"></polyline>
                            <line x1="21" y1="12" x2="9" y2="12"></line>
                        </svg>
                        <span class="nav-text">Logout</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <div class="main-content">
            <header class="header">
                <div class="header-left">
                    <h1 class="header-title">Dummy Data Generator</h1>
                    <div class="breadcrumb">
                        <span>System</span>
                        <span class="breadcrumb-separator">/</span>
                        <span>Management</span>
                        <span class="breadcrumb-separator">/</span>
                        <span>Dummy Data</span>
                    </div>
                </div>
                <div class="header-right">
                    <p class="page-subtitle">Generate test data for development and testing purposes</p>
                </div>
            </header>

            <main class="main">
                <div class="container">

            <?php if ($message): ?>
                <div class="alert alert-<?= $messageType ?>">
                    <?= $message ?>
                </div>
            <?php endif; ?>

            <div class="form-container">
                <form method="post" class="dummy-data-form">
                    <div class="form-section">
                        <h2>Select Tenant</h2>
                        
                        <div class="form-group">
                            <label for="tenant_id">Tenant</label>
                            <select id="tenant_id" name="tenant_id" required>
                                <option value="">Select a tenant...</option>
                                <?php foreach ($tenants as $tenant): ?>
                                    <option value="<?= $tenant['id'] ?>" 
                                            <?= (($_POST['tenant_id'] ?? '') === $tenant['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($tenant['business_name']) ?> (<?= htmlspecialchars($tenant['subdomain']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2>Data Types to Generate</h2>
                        
                        <div class="checkbox-grid">
                            <label class="checkbox-label">
                                <input type="checkbox" name="data_types[]" value="all" id="select_all">
                                <strong>Generate All Data</strong>
                                <small>Complete dataset with all types</small>
                            </label>
                            
                            <hr>
                            
                            <label class="checkbox-label">
                                <input type="checkbox" name="data_types[]" value="categories" class="data-type">
                                Service Categories
                                <small>Hair, Beauty, Wellness, etc.</small>
                            </label>
                            
                            <label class="checkbox-label">
                                <input type="checkbox" name="data_types[]" value="services" class="data-type">
                                Services
                                <small>Haircut, Massage, Facial, etc.</small>
                            </label>
                            
                            <label class="checkbox-label">
                                <input type="checkbox" name="data_types[]" value="employees" class="data-type">
                                Employees
                                <small>Staff members and their schedules</small>
                            </label>
                            
                            <label class="checkbox-label">
                                <input type="checkbox" name="data_types[]" value="customers" class="data-type">
                                Customers
                                <small>Test customer accounts</small>
                            </label>
                            
                            <label class="checkbox-label">
                                <input type="checkbox" name="data_types[]" value="reservations" class="data-type">
                                Reservations
                                <small>Sample bookings (past and future)</small>
                            </label>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Generate Dummy Data</button>
                        <button type="button" onclick="clearForm()" class="btn btn-secondary">Clear Selection</button>
                    </div>
                </form>
            </div>

            <div class="info-section">
                <h2>About Dummy Data</h2>
                <div class="info-content">
                    <div class="info-card">
                        <h3>⚠️ Important Notes</h3>
                        <ul>
                            <li>Dummy data is for testing purposes only</li>
                            <li>It will be mixed with existing data</li>
                            <li>Use the Database Tools to clean up if needed</li>
                            <li>Generated data is in Greek and English</li>
                        </ul>
                    </div>
                    
                    <div class="info-card">
                        <h3>📊 What Gets Generated</h3>
                        <ul>
                            <li><strong>Categories:</strong> 4 service categories with icons</li>
                            <li><strong>Services:</strong> 10 services with realistic pricing</li>
                            <li><strong>Employees:</strong> 3 staff members with service assignments</li>
                            <li><strong>Customers:</strong> 6 test customers with emails</li>
                            <li><strong>Reservations:</strong> 15-30 bookings across 2 weeks</li>
                        </ul>
                    </div>
                    
                    <div class="info-card">
                        <h3>🔧 Usage Tips</h3>
                        <ul>
                            <li>Generate categories and services first</li>
                            <li>Add employees before creating reservations</li>
                            <li>Use "Generate All" for complete test environment</li>
                            <li>Check the results in the tenant's admin panel</li>
                        </ul>
                    </div>
                </div>
            </div>

            <?php if (!empty($tenants)): ?>
                <div class="tenant-overview">
                    <h2>Tenant Overview</h2>
                    <div class="tenant-grid">
                        <?php foreach ($tenants as $tenant): ?>
                            <div class="tenant-card">
                                <h3><?= htmlspecialchars($tenant['business_name']) ?></h3>
                                <p class="tenant-subdomain"><?= htmlspecialchars($tenant['subdomain']) ?></p>
                                <p class="tenant-status">
                                    <span class="status-badge <?= $tenant['status'] ?>">
                                        <?= ucfirst($tenant['status']) ?>
                                    </span>
                                </p>
                                <div class="tenant-actions">
                                    <button onclick="selectTenant('<?= $tenant['id'] ?>')" class="btn btn-sm btn-primary">
                                        Select
                                    </button>
                                    <a href="https://<?= htmlspecialchars($tenant['subdomain']) ?>.skrtz.gr/store-admin"
                                       target="_blank" class="btn btn-sm btn-secondary">
                                        Admin
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
                </div>
            </main>
        </div>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay"></div>

    <script src="assets/system.js"></script>
    <script>
        // Handle "Select All" checkbox
        document.getElementById('select_all').addEventListener('change', function() {
            const dataTypeCheckboxes = document.querySelectorAll('.data-type');
            const isChecked = this.checked;
            
            dataTypeCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                checkbox.disabled = isChecked;
            });
        });

        // Handle individual checkboxes
        document.querySelectorAll('.data-type').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const selectAllCheckbox = document.getElementById('select_all');
                const anyChecked = document.querySelectorAll('.data-type:checked').length > 0;
                
                if (anyChecked) {
                    selectAllCheckbox.checked = false;
                }
            });
        });

        function selectTenant(tenantId) {
            document.getElementById('tenant_id').value = tenantId;
            document.getElementById('tenant_id').focus();
        }

        function clearForm() {
            document.getElementById('tenant_id').value = '';
            document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = false;
                checkbox.disabled = false;
            });
        }
    </script>
</body>
</html>
