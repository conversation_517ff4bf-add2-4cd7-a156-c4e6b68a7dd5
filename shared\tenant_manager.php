<?php

/**
 * Tenant Management System
 * Handles multi-tenant routing and context
 */

require_once __DIR__ . '/database.php';

class TenantManager
{
    private static ?string $currentTenant = null;
    private static ?Database $database = null;
    private static bool $initialized = false;
    
    public static function init(): void
    {
        if (self::$initialized) {
            return;
        }
        
        self::detectTenant();
        self::$initialized = true;
    }
    
    private static function detectTenant(): void
    {
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

        // Remove port if present
        $host = explode(':', $host)[0];

        // Check for subdomain
        $parts = explode('.', $host);

        if (count($parts) > 2) {
            // Subdomain detected
            $detectedTenant = $parts[0];

            // Check if tenant exists in database before setting it
            if (self::tenantExists($detectedTenant)) {
                self::$currentTenant = $detectedTenant;
            } else {
                // Tenant doesn't exist, redirect to demo
                self::$currentTenant = 'demo';

                // If we're not already on demo, redirect
                if ($detectedTenant !== 'demo') {
                    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                    $redirectUrl = $protocol . '://demo.' . implode('.', array_slice($parts, 1));
                    header('Location: ' . $redirectUrl);
                    exit;
                }
            }
        } elseif (isset($_GET['tenant'])) {
            // URL parameter fallback
            $paramTenant = $_GET['tenant'];
            if (self::tenantExists($paramTenant)) {
                self::$currentTenant = $paramTenant;
            } else {
                self::$currentTenant = 'demo';
            }
        } else {
            // Default tenant for development
            self::$currentTenant = 'demo';
        }

        // Validate tenant name
        if (!self::isValidTenantName(self::$currentTenant)) {
            self::$currentTenant = 'demo';
        }
    }
    
    private static function isValidTenantName(string $name): bool
    {
        return preg_match('/^[a-zA-Z0-9_-]+$/', $name) && strlen($name) <= 50;
    }
    
    public static function getCurrentTenant(): ?string
    {
        return self::$currentTenant;
    }
    
    public static function getDatabase(): Database
    {
        if (self::$database === null) {
            self::$database = Database::tenantSafe(self::$currentTenant);
        }

        return self::$database;
    }
    
    public static function getSystemDatabase(): Database
    {
        return Database::getInstance('system');
    }
    
    public static function createTenant(array $tenantData): array
    {
        $subdomain = $tenantData['subdomain'] ?? '';
        $businessName = $tenantData['business_name'] ?? '';
        $ownerName = $tenantData['owner_name'] ?? '';
        $ownerEmail = $tenantData['owner_email'] ?? '';
        $adminUsername = $tenantData['admin_username'] ?? $subdomain;
        $adminPassword = $tenantData['admin_password'] ?? $subdomain . '123';

        if (!self::isValidTenantName($subdomain)) {
            return ['success' => false, 'error' => 'Invalid subdomain format'];
        }

        if (empty($businessName)) {
            return ['success' => false, 'error' => 'Business name is required'];
        }

        try {
            $systemDb = self::getSystemDatabase();

            // Check if tenant already exists
            $existing = $systemDb->fetchRow(
                "SELECT id FROM tenants WHERE subdomain = :subdomain",
                [':subdomain' => $subdomain]
            );

            if ($existing) {
                return ['success' => false, 'error' => 'Subdomain already exists'];
            }

            // Create tenant record
            $systemDb->query(
                "INSERT INTO tenants (subdomain, business_name, owner_name, owner_email, status, created_at) VALUES (:subdomain, :business_name, :owner_name, :owner_email, :status, :created_at)",
                [
                    ':subdomain' => $subdomain,
                    ':business_name' => $businessName,
                    ':owner_name' => $ownerName,
                    ':owner_email' => $ownerEmail,
                    ':status' => 'active',
                    ':created_at' => date('Y-m-d H:i:s')
                ]
            );

            $tenantId = $systemDb->lastInsertId();

            // Create tenant database
            $tenantDb = Database::getInstance($subdomain);

            // Initialize default settings
            self::initializeTenantDefaults($tenantDb, $businessName, $adminUsername, $adminPassword);

            return [
                'success' => true,
                'tenant_id' => $tenantId,
                'subdomain' => $subdomain,
                'admin_username' => $adminUsername,
                'admin_password' => $adminPassword
            ];

        } catch (Exception $e) {
            error_log("Failed to create tenant: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    private static function initializeTenantDefaults(Database $db, string $businessName = 'My Business', string $adminUsername = 'admin', string $adminPassword = 'admin123'): void
    {
        // Default settings
        $defaultSettings = [
            'business_name' => $businessName,
            'business_phone' => '',
            'business_email' => '',
            'business_address' => '',
            'default_language' => 'en',
            'timezone' => 'Europe/Athens',
            'booking_enabled' => '1',
            'verification_required' => '1',
            'email_notifications' => '1',
            'admin_username' => $adminUsername,
            'admin_password' => password_hash($adminPassword, PASSWORD_DEFAULT)
        ];
        
        foreach ($defaultSettings as $key => $value) {
            $db->query(
                "INSERT INTO settings (key, value) VALUES (:key, :value)",
                [':key' => $key, ':value' => $value]
            );
        }
        
        // Default working hours
        $workingHours = [
            1 => ['09:00', '17:00'], // Monday
            2 => ['09:00', '17:00'], // Tuesday
            3 => ['09:00', '17:00'], // Wednesday
            4 => ['09:00', '17:00'], // Thursday
            5 => ['09:00', '17:00'], // Friday
            6 => ['09:00', '15:00'], // Saturday
            0 => null                // Sunday (closed)
        ];
        
        foreach ($workingHours as $day => $hours) {
            if ($hours) {
                $db->query(
                    "INSERT INTO working_hours (day_of_week, start_time, end_time) VALUES (:day, :start, :end)",
                    [':day' => $day, ':start' => $hours[0], ':end' => $hours[1]]
                );
            } else {
                $db->query(
                    "INSERT INTO working_hours (day_of_week, is_active) VALUES (:day, 0)",
                    [':day' => $day]
                );
            }
        }
        
        // Default texts
        $defaultTexts = [
            'booking_title' => ['en' => 'Book Appointment', 'el' => 'Κλείσιμο Ραντεβού'],
            'select_service' => ['en' => 'Select Service', 'el' => 'Επιλέξτε Υπηρεσία'],
            'select_date' => ['en' => 'Select Date', 'el' => 'Επιλέξτε Ημερομηνία'],
            'select_time' => ['en' => 'Select Time', 'el' => 'Επιλέξτε Ώρα'],
            'contact_details' => ['en' => 'Contact Details', 'el' => 'Στοιχεία Επικοινωνίας'],
            'confirmation' => ['en' => 'Confirmation', 'el' => 'Επιβεβαίωση'],
            'name' => ['en' => 'Name', 'el' => 'Όνομα'],
            'email' => ['en' => 'Email', 'el' => 'Email'],
            'phone' => ['en' => 'Phone', 'el' => 'Τηλέφωνο'],
            'next' => ['en' => 'Next', 'el' => 'Επόμενο'],
            'back' => ['en' => 'Back', 'el' => 'Πίσω'],
            'confirm' => ['en' => 'Confirm', 'el' => 'Επιβεβαίωση'],
            'cancel' => ['en' => 'Cancel', 'el' => 'Ακύρωση']
        ];
        
        foreach ($defaultTexts as $key => $languages) {
            foreach ($languages as $lang => $text) {
                $db->query(
                    "INSERT INTO texts (id, text_key, text_value, language, category, created_at, updated_at) VALUES (:id, :key, :value, :lang, :category, :created, :updated)",
                    [
                        ':id' => 'TXT' . uniqid(),
                        ':key' => $key,
                        ':value' => $text,
                        ':lang' => $lang,
                        ':category' => 'ui',
                        ':created' => date('Y-m-d H:i:s'),
                        ':updated' => date('Y-m-d H:i:s')
                    ]
                );
            }
        }
        
        // Default admin user
        $hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
        $db->query(
            "INSERT INTO admin_users (username, password_hash, email, created_at) VALUES (:username, :password, :email, :created_at)",
            [
                ':username' => $adminUsername,
                ':password' => $hashedPassword,
                ':email' => '<EMAIL>',
                ':created_at' => date('Y-m-d H:i:s')
            ]
        );
    }
    
    public static function deleteTenant($identifier): bool
    {
        try {
            $systemDb = self::getSystemDatabase();

            // Get tenant info - identifier could be ID or subdomain
            if (is_numeric($identifier)) {
                $tenant = $systemDb->fetchRow(
                    "SELECT * FROM tenants WHERE id = :id",
                    [':id' => $identifier]
                );
            } else {
                $tenant = $systemDb->fetchRow(
                    "SELECT * FROM tenants WHERE subdomain = :subdomain",
                    [':subdomain' => $identifier]
                );
            }

            if (!$tenant) {
                return false;
            }

            // Delete tenant record
            $systemDb->query(
                "DELETE FROM tenants WHERE id = :id",
                [':id' => $tenant['id']]
            );

            // Close any open database connections for this tenant
            Database::closeInstance($tenant['subdomain']);

            // Delete tenant database file
            $dbPath = __DIR__ . "/../data/tenants/{$tenant['subdomain']}.db";
            if (file_exists($dbPath)) {
                // On Windows, we might need to wait a moment for the file handle to be released
                $attempts = 0;
                while ($attempts < 5) {
                    if (@unlink($dbPath)) {
                        break;
                    }
                    $attempts++;
                    usleep(100000); // Wait 100ms
                }

                if (file_exists($dbPath)) {
                    error_log("Warning: Could not delete database file: $dbPath");
                }
            }

            return true;

        } catch (Exception $e) {
            error_log("Failed to delete tenant: " . $e->getMessage());
            return false;
        }
    }
    
    public static function listTenants(): array
    {
        try {
            $systemDb = self::getSystemDatabase();
            return $systemDb->fetchAll("SELECT * FROM tenants ORDER BY business_name");
        } catch (Exception $e) {
            error_log("Failed to list tenants: " . $e->getMessage());
            return [];
        }
    }

    public static function tenantExists(string $subdomain): bool
    {
        try {
            $systemDb = self::getSystemDatabase();
            $result = $systemDb->fetchRow(
                "SELECT id FROM tenants WHERE subdomain = :subdomain AND status = 'active'",
                [':subdomain' => $subdomain]
            );
            return $result !== null;
        } catch (Exception $e) {
            return false;
        }
    }
}
