<?php
/**
 * Reservations View
 * Manage reservations
 */

// Get search and pagination parameters
$searchParams = AdminHelpers::getSearchParams();
$search = $searchParams['search'];
$pageNum = $searchParams['page_num'];
$perPage = $searchParams['per_page'];

// Additional filters
$statusFilter = $_GET['status'] ?? '';
$dateFilter = $_GET['date'] ?? '';
$customerFilter = $_GET['customer_id'] ?? '';

// Build search query - search in customer name/email/phone, service name, employee name/phone, and notes
$searchWhere = AdminHelpers::buildSearchWhere($search, ['c.name', 'c.email', 'c.phone', 's.name', 'e.name', 'e.phone', 'r.notes']);

// Add additional filters
$additionalWhere = [];
$additionalParams = [];

if ($statusFilter) {
    $additionalWhere[] = "r.status = :status_filter";
    $additionalParams[':status_filter'] = $statusFilter;
}

if ($dateFilter) {
    $additionalWhere[] = "r.date = :date_filter";
    $additionalParams[':date_filter'] = $dateFilter;
}

if ($customerFilter) {
    $additionalWhere[] = "r.customer_id = :customer_filter";
    $additionalParams[':customer_filter'] = $customerFilter;
}

// Combine where clauses
$whereClause = $searchWhere['where'];
if (!empty($additionalWhere)) {
    if ($whereClause) {
        $whereClause .= " AND " . implode(' AND ', $additionalWhere);
    } else {
        $whereClause = "WHERE " . implode(' AND ', $additionalWhere);
    }
}

$allParams = array_merge($searchWhere['params'], $additionalParams);

// Get total count for pagination first
$countSql = "SELECT COUNT(*) as total
             FROM reservations r
             LEFT JOIN customers c ON r.customer_id = c.id
             LEFT JOIN services s ON r.service_id = s.id
             LEFT JOIN employees e ON r.employee_id = e.id
             {$whereClause}";
$totalCount = $db->fetchRow($countSql, $allParams)['total'];

// Create pagination
$pagination = new Pagination($totalCount, $perPage, $pageNum);

// Get reservations with related data using proper SQL pagination
$sql = "SELECT r.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone,
               s.name as service_name, s.duration as service_duration, s.price as service_price,
               e.name as employee_name, e.color as employee_color
        FROM reservations r
        LEFT JOIN customers c ON r.customer_id = c.id
        LEFT JOIN services s ON r.service_id = s.id
        LEFT JOIN employees e ON r.employee_id = e.id
        {$whereClause}
        ORDER BY r.date DESC, r.start_time DESC
        LIMIT {$perPage} OFFSET {$pagination->getOffset()}";
$paginatedReservations = $db->fetchAll($sql, $allParams);

// Get filter options
$statuses = ['pending', 'confirmed', 'completed', 'cancelled'];
?>

<!-- Enhanced Toolbar -->
<div class="toolbar">
    <div class="toolbar-left">
        <div class="search-bar">
            <input type="text" placeholder="Search by customer, service, employee..." value="<?php echo htmlspecialchars($search); ?>" id="reservation-search">
            <i class="fas fa-search search-icon"></i>
        </div>
        <div class="filter-group">
            <span class="filter-label">Date:</span>
            <select class="form-control" id="date-filter">
                <option value="">All Dates</option>
                <option value="today">Today</option>
                <option value="tomorrow">Tomorrow</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
            </select>
        </div>
        <div class="filter-group">
            <span class="filter-label">Status:</span>
            <select class="form-control" id="status-filter">
                <option value="">All Status</option>
                <option value="confirmed">Confirmed</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
                <option value="no-show">No Show</option>
            </select>
        </div>
        <div class="filter-group">
            <span class="filter-label">Employee:</span>
            <select class="form-control" id="employee-filter">
                <option value="">All Employees</option>
                <?php
                $employees = $db->fetchAll("SELECT id, name FROM employees WHERE is_active = 1 ORDER BY name");
                foreach ($employees as $emp): ?>
                    <option value="<?php echo $emp['id']; ?>"><?php echo htmlspecialchars($emp['name']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
    </div>
    <div class="toolbar-right">
        <div class="view-toggle">
            <button class="active" data-view="cards">
                <i class="fas fa-th-large"></i> Cards
            </button>
            <button data-view="timeline">
                <i class="fas fa-calendar-alt"></i> Timeline
            </button>
            <button data-view="table">
                <i class="fas fa-list"></i> Table
            </button>
        </div>
        <button class="btn btn-secondary" onclick="showCalendar()">
            <i class="fas fa-calendar"></i> Calendar
        </button>
        <button class="btn btn-primary" onclick="addReservation()">
            <i class="fas fa-plus"></i> New Booking
        </button>
    </div>
</div>

<!-- Bulk Actions -->
<div class="bulk-actions" id="bulk-actions">
    <span class="bulk-actions-text">
        <span id="selected-count">0</span> reservations selected
    </span>
    <div class="bulk-actions-buttons">
        <button class="btn btn-secondary btn-sm" onclick="bulkConfirm()">
            <i class="fas fa-check"></i> Confirm
        </button>
        <button class="btn btn-secondary btn-sm" onclick="bulkRemind()">
            <i class="fas fa-bell"></i> Send Reminders
        </button>
        <button class="btn btn-secondary btn-sm" onclick="bulkReschedule()">
            <i class="fas fa-calendar-alt"></i> Reschedule
        </button>
        <button class="btn btn-danger btn-sm" onclick="bulkCancel()">
            <i class="fas fa-times"></i> Cancel
        </button>
    </div>
</div>

<!-- Old filter section removed - using enhanced toolbar above -->

<!-- Enhanced Reservations Grid -->
<div class="entity-grid" id="reservations-grid">
    <?php if (empty($paginatedReservations)): ?>
        <div class="empty-state">
            <i class="fas fa-calendar-alt fa-4x text-muted"></i>
            <h3>No reservations found</h3>
            <p>Start by creating your first reservation or adjust your search criteria.</p>
            <button class="btn btn-primary btn-lg" onclick="addReservation()">
                <i class="fas fa-plus"></i> New Reservation
            </button>
        </div>
    <?php else: ?>
        <?php foreach ($paginatedReservations as $reservation): ?>
            <?php
            $reservationDate = new DateTime($reservation['date']);
            $today = new DateTime();
            $isToday = $reservationDate->format('Y-m-d') === $today->format('Y-m-d');
            $isPast = $reservationDate < $today;
            $isFuture = $reservationDate > $today;

            $statusColors = [
                'confirmed' => 'success',
                'completed' => 'info',
                'cancelled' => 'danger',
                'pending' => 'warning'
            ];
            $statusColor = $statusColors[$reservation['status']] ?? 'secondary';
            ?>
            <div class="entity-card reservation-card <?php echo $reservation['status']; ?>" data-id="<?php echo $reservation['id']; ?>">
                <div class="entity-card-header">
                    <input type="checkbox" class="entity-select" value="<?php echo $reservation['id']; ?>">
                    <div class="entity-status-indicator <?php echo $statusColor === 'success' ? 'active' : ($statusColor === 'warning' ? 'warning' : 'inactive'); ?>"></div>
                    <h3 class="entity-title"><?php echo htmlspecialchars($reservation['customer_name']); ?></h3>
                    <div class="entity-subtitle">
                        <span class="status-badge status-<?php echo $reservation['status']; ?>">
                            <?php echo ucfirst($reservation['status']); ?>
                        </span>
                    </div>
                </div>

                <div class="entity-card-body">
                    <div class="reservation-datetime">
                        <div class="datetime-main">
                            <div class="date-info">
                                <i class="fas fa-calendar"></i>
                                <span class="date-value <?php echo $isToday ? 'today' : ($isPast ? 'past' : 'future'); ?>">
                                    <?php if ($isToday): ?>
                                        Today
                                    <?php else: ?>
                                        <?php echo $reservationDate->format('M j, Y'); ?>
                                    <?php endif; ?>
                                </span>
                            </div>
                            <div class="time-info">
                                <i class="fas fa-clock"></i>
                                <span class="time-value"><?php echo date('H:i', strtotime($reservation['start_time'])); ?></span>
                                <?php if ($reservation['end_time']): ?>
                                    <span class="time-separator">-</span>
                                    <span class="time-value"><?php echo date('H:i', strtotime($reservation['end_time'])); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="entity-info">
                        <div class="info-row">
                            <span class="info-label">
                                <i class="fas fa-cut"></i> Service
                            </span>
                            <span class="info-value">
                                <?php echo htmlspecialchars($reservation['service_name']); ?>
                            </span>
                        </div>

                        <div class="info-row">
                            <span class="info-label">
                                <i class="fas fa-user-tie"></i> Employee
                            </span>
                            <span class="info-value">
                                <?php if ($reservation['employee_name']): ?>
                                    <span class="employee-badge" style="background-color: <?php echo $reservation['employee_color'] ?: '#e2e8f0'; ?>;">
                                        <?php echo htmlspecialchars($reservation['employee_name']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted">Not assigned</span>
                                <?php endif; ?>
                            </span>
                        </div>

                        <?php if ($reservation['customer_phone']): ?>
                            <div class="info-row">
                                <span class="info-label">
                                    <i class="fas fa-phone"></i> Phone
                                </span>
                                <a href="tel:<?php echo htmlspecialchars($reservation['customer_phone']); ?>" class="info-value contact-link">
                                    <?php echo htmlspecialchars($reservation['customer_phone']); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="entity-stats">
                        <div class="stat-item">
                            <div class="stat-value">€<?php echo number_format($reservation['price'], 0); ?></div>
                            <div class="stat-label">Price</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value"><?php echo $reservation['service_duration']; ?>min</div>
                            <div class="stat-label">Duration</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">
                                <?php if ($isPast): ?>
                                    <i class="fas fa-history text-muted"></i>
                                <?php elseif ($isToday): ?>
                                    <i class="fas fa-clock text-warning"></i>
                                <?php else: ?>
                                    <i class="fas fa-calendar-check text-success"></i>
                                <?php endif; ?>
                            </div>
                            <div class="stat-label">
                                <?php echo $isPast ? 'Past' : ($isToday ? 'Today' : 'Upcoming'); ?>
                            </div>
                        </div>
                    </div>

                    <div class="entity-actions">
                        <button class="btn btn-sm btn-outline" onclick="viewReservation('<?php echo $reservation['id']; ?>')">
                            <i class="fas fa-eye"></i> View
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editReservation('<?php echo $reservation['id']; ?>')">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <?php if ($reservation['status'] === 'confirmed'): ?>
                            <button class="btn btn-sm btn-success" onclick="completeReservation('<?php echo $reservation['id']; ?>')">
                                <i class="fas fa-check"></i> Complete
                            </button>
                        <?php elseif ($reservation['status'] === 'pending'): ?>
                            <button class="btn btn-sm btn-success" onclick="confirmReservation('<?php echo $reservation['id']; ?>')">
                                <i class="fas fa-check"></i> Confirm
                            </button>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="entity-footer">
                    Created: <?php echo date('M j, Y', strtotime($reservation['created_at'])); ?>
                    <?php if ($reservation['notes']): ?>
                        <span class="has-notes">• Has notes</span>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; ?>
            <div class="card reservation-card">
                <div class="card-header">
                    <div class="reservation-datetime">
                        <div class="reservation-date">
                            <?php
                            if ($reservation['date']) {
                                echo date('M j, Y', strtotime($reservation['date']));
                            } else {
                                echo 'Date not set';
                            }
                            ?>
                        </div>
                        <div class="reservation-time">
                            <?php
                            if ($reservation['start_time']) {
                                echo date('g:i A', strtotime($reservation['start_time']));
                                if (!empty($reservation['end_time'])) {
                                    echo ' - ' . date('g:i A', strtotime($reservation['end_time']));
                                }
                            } else {
                                echo 'Time not set';
                            }
                            ?>
                        </div>
                    </div>
                    
                    <div class="reservation-status-actions">
                        <?php echo AdminHelpers::renderStatusBadge($reservation['status']); ?>
                        <?php echo AdminHelpers::renderActionButtonsWithCheck($db, 'reservations', $reservation['id']); ?>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="reservation-main-info">
                        <div class="customer-info">
                            <h4><i class="fas fa-user"></i> <?php echo htmlspecialchars($reservation['customer_name']); ?></h4>
                            <?php echo AdminHelpers::formatContactInfo([
                                'email' => $reservation['customer_email'],
                                'phone' => $reservation['customer_phone']
                            ]); ?>
                        </div>
                        
                        <div class="service-info">
                            <h4><i class="fas fa-concierge-bell"></i> <?php echo htmlspecialchars($reservation['service_name']); ?></h4>
                            <div class="service-details">
                                <span>Duration: <?php echo $reservation['service_duration']; ?> minutes</span>
                                <span>Price: <?php echo AdminHelpers::formatCurrency($reservation['service_price']); ?></span>
                            </div>
                        </div>
                        
                        <?php if ($reservation['employee_name']): ?>
                            <div class="employee-info">
                                <h4>
                                    <div class="employee-avatar" style="background-color: <?php echo htmlspecialchars($reservation['employee_color'] ?: '#3498db'); ?>">
                                        <?php echo strtoupper(substr($reservation['employee_name'], 0, 1)); ?>
                                    </div>
                                    <?php echo htmlspecialchars($reservation['employee_name']); ?>
                                </h4>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($reservation['notes']): ?>
                        <div class="reservation-notes">
                            <strong>Notes:</strong>
                            <p><?php echo htmlspecialchars($reservation['notes']); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <div class="card-date">
                        <?php echo date('j/n/Y', strtotime($reservation['created_at'])); ?>
                    </div>
                </div>
            </div>

        <!-- Add Reservation Card -->
        <div class="entity-card add-card" onclick="addReservation()">
            <div class="entity-card-body" style="display: flex; align-items: center; justify-content: center; min-height: 200px; flex-direction: column; cursor: pointer;">
                <i class="fas fa-plus fa-3x text-muted" style="margin-bottom: 16px;"></i>
                <h3 style="color: var(--text-muted); margin: 0;">New Reservation</h3>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if ($pagination->getTotalPages() > 1): ?>
    <div class="pagination-container">
        <?php echo $pagination->render(); ?>
    </div>
<?php endif; ?>

<script>
function applyFilter(type, value) {
    const url = new URL(window.location);
    if (value) {
        url.searchParams.set(type, value);
    } else {
        url.searchParams.delete(type);
    }
    url.searchParams.set('page_num', 1); // Reset to first page
    window.location.href = url.toString();
}

function removeFilter(type) {
    const url = new URL(window.location);
    url.searchParams.delete(type);
    url.searchParams.set('page_num', 1);
    window.location.href = url.toString();
}

function clearAllFilters() {
    const url = new URL(window.location);
    url.searchParams.delete('status');
    url.searchParams.delete('date');
    url.searchParams.delete('customer_id');
    url.searchParams.set('page_num', 1);
    window.location.href = url.toString();
}
</script>

<style>
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.page-actions {
    display: flex;
    gap: 10px;
}

.filters-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.filter-select,
.filter-date {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
}

.active-filters {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    padding: 15px;
    background: var(--light-color);
    border-radius: 4px;
}

.filter-tag {
    display: flex;
    align-items: center;
    gap: 5px;
    background: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.875rem;
}

.filter-tag button {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    margin-left: 5px;
}

.reservation-card {
    border-left: 4px solid var(--primary-color);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.reservation-datetime {
    text-align: left;
}

.reservation-date {
    font-weight: 600;
    color: var(--secondary-color);
    font-size: 1.1rem;
}

.reservation-time {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.reservation-status-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.reservation-main-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 15px;
}

.customer-info h4,
.service-info h4,
.employee-info h4 {
    margin: 0 0 10px 0;
    font-size: 1rem;
    color: var(--secondary-color);
}

.service-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.employee-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 12px;
    margin-right: 8px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.reservation-notes {
    background: var(--light-color);
    padding: 15px;
    border-radius: 4px;
    margin: 15px 0;
}

.reservation-notes p {
    margin: 5px 0 0 0;
    color: var(--text-muted);
}

.reservation-meta {
    border-top: 1px solid var(--border-color);
    padding-top: 15px;
    margin-top: 15px;
}

.pagination-container {
    margin-top: 30px;
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .page-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .search-filter-bar {
        flex-direction: column;
        gap: 15px;
    }
    
    .filters-group {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .reservation-main-info {
        grid-template-columns: 1fr;
    }
    
    .card-header {
        flex-direction: column;
        gap: 10px;
    }
    
    .reservation-status-actions {
        justify-content: center;
    }
}
</style>
