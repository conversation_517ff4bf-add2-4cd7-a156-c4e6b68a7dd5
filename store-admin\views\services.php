<?php
/**
 * Services View
 * Manage services
 */

// Get search and pagination parameters
$searchParams = AdminHelpers::getSearchParams();
$search = $searchParams['search'];
$pageNum = $searchParams['page_num'];
$perPage = $searchParams['per_page'];

// Build search query - search by service name, description, category name, and price
$searchWhere = AdminHelpers::buildSearchWhere($search, ['s.name', 's.description', 'c.name', 's.price']);

// Get total count for pagination first
$countSql = "SELECT COUNT(*) as total
             FROM services s
             LEFT JOIN categories c ON s.category_id = c.id
             {$searchWhere['where']}";
$totalCount = $db->fetchRow($countSql, $searchWhere['params'])['total'];

// Create pagination
$pagination = new Pagination($totalCount, $perPage, $pageNum);

// Get services with categories using proper SQL pagination
$sql = "SELECT s.*, c.name as category_name, c.color as category_color
        FROM services s
        LEFT JOIN categories c ON s.category_id = c.id
        {$searchWhere['where']}
        ORDER BY s.name ASC
        LIMIT {$perPage} OFFSET {$pagination->getOffset()}";
$paginatedServices = $db->fetchAll($sql, $searchWhere['params']);
?>

<!-- Enhanced Toolbar -->
<div class="toolbar">
    <div class="toolbar-left">
        <div class="search-bar">
            <input type="text" placeholder="Search services, categories, prices..." value="<?php echo htmlspecialchars($search); ?>" id="service-search">
            <i class="fas fa-search search-icon"></i>
        </div>
        <div class="filter-group">
            <span class="filter-label">Category:</span>
            <select class="form-control" id="category-filter">
                <option value="">All Categories</option>
                <?php
                $categories = $db->fetchAll("SELECT DISTINCT c.id, c.name FROM categories c JOIN services s ON c.id = s.category_id ORDER BY c.name");
                foreach ($categories as $category): ?>
                    <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="filter-group">
            <span class="filter-label">Price Range:</span>
            <select class="form-control" id="price-filter">
                <option value="">All Prices</option>
                <option value="0-25">€0 - €25</option>
                <option value="25-50">€25 - €50</option>
                <option value="50-100">€50 - €100</option>
                <option value="100+">€100+</option>
            </select>
        </div>
        <div class="filter-group">
            <span class="filter-label">Duration:</span>
            <select class="form-control" id="duration-filter">
                <option value="">All Durations</option>
                <option value="0-30">0-30 min</option>
                <option value="30-60">30-60 min</option>
                <option value="60-120">1-2 hours</option>
                <option value="120+">2+ hours</option>
            </select>
        </div>
    </div>
    <div class="toolbar-right">
        <div class="view-toggle">
            <button class="active" data-view="cards">
                <i class="fas fa-th-large"></i> Cards
            </button>
            <button data-view="table">
                <i class="fas fa-list"></i> Table
            </button>
        </div>
        <button class="btn btn-secondary" onclick="manageCategories()">
            <i class="fas fa-tags"></i> Categories
        </button>
        <button class="btn btn-primary" onclick="addService()">
            <i class="fas fa-plus"></i> Add Service
        </button>
    </div>
</div>

<!-- Bulk Actions -->
<div class="bulk-actions" id="bulk-actions">
    <span class="bulk-actions-text">
        <span id="selected-count">0</span> services selected
    </span>
    <div class="bulk-actions-buttons">
        <button class="btn btn-secondary btn-sm" onclick="bulkUpdatePrices()">
            <i class="fas fa-euro-sign"></i> Update Prices
        </button>
        <button class="btn btn-secondary btn-sm" onclick="bulkDuplicate()">
            <i class="fas fa-copy"></i> Duplicate
        </button>
        <button class="btn btn-secondary btn-sm" onclick="bulkToggleStatus()">
            <i class="fas fa-toggle-on"></i> Toggle Status
        </button>
        <button class="btn btn-danger btn-sm" onclick="bulkDelete()">
            <i class="fas fa-trash"></i> Delete
        </button>
    </div>
</div>

<!-- Enhanced Services Grid -->
<div class="entity-grid" id="services-grid">
    <?php if (empty($paginatedServices)): ?>
        <div class="empty-state">
            <i class="fas fa-cut fa-4x text-muted"></i>
            <h3>No services found</h3>
            <p>Start by adding your first service or adjust your search criteria.</p>
            <button class="btn btn-primary btn-lg" onclick="addService()">
                <i class="fas fa-plus"></i> Add First Service
            </button>
        </div>
    <?php else: ?>
        <?php foreach ($paginatedServices as $service): ?>
            <?php
            $totalDuration = ($service['preparation_time'] ?? 0) + $service['duration'] + ($service['cleanup_time'] ?? 0);
            $popularity = 'standard'; // Could be calculated from booking data
            if ($service['price'] > 50) $popularity = 'premium';
            if ($service['price'] < 20) $popularity = 'basic';
            ?>
            <div class="entity-card service-card" data-id="<?php echo $service['id']; ?>">
                <div class="entity-card-header">
                    <input type="checkbox" class="entity-select" value="<?php echo $service['id']; ?>">
                    <div class="entity-status-indicator <?php echo $service['is_active'] ? 'active' : 'inactive'; ?>"></div>
                    <h3 class="entity-title"><?php echo htmlspecialchars($service['name']); ?></h3>
                    <div class="entity-subtitle">
                        <?php if ($service['category_name']): ?>
                            <span class="category-badge" style="background-color: <?php echo htmlspecialchars($service['category_color'] ?: '#e2e8f0'); ?>; color: <?php echo $service['category_color'] ? '#fff' : '#64748b'; ?>;">
                                <?php echo htmlspecialchars($service['category_name']); ?>
                            </span>
                        <?php else: ?>
                            Uncategorized
                        <?php endif; ?>
                    </div>
                </div>

                <div class="entity-card-body">
                    <?php if ($service['description']): ?>
                        <div class="entity-info">
                            <div class="info-row">
                                <span class="info-label">
                                    <i class="fas fa-info-circle"></i> Description
                                </span>
                                <span class="info-value">
                                    <?php echo htmlspecialchars(substr($service['description'], 0, 60)); ?><?php echo strlen($service['description']) > 60 ? '...' : ''; ?>
                                </span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="entity-stats">
                        <div class="stat-item">
                            <div class="stat-value">€<?php echo number_format($service['price'], 0); ?></div>
                            <div class="stat-label">Price</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value"><?php echo $service['duration']; ?>min</div>
                            <div class="stat-label">Duration</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value"><?php echo $totalDuration; ?>min</div>
                            <div class="stat-label">Total Time</div>
                        </div>
                    </div>

                    <?php if ($service['preparation_time'] > 0 || $service['cleanup_time'] > 0): ?>
                        <div class="service-timing">
                            <div class="timing-breakdown">
                                <?php if ($service['preparation_time'] > 0): ?>
                                    <span class="timing-item prep">
                                        <i class="fas fa-clock"></i> Prep: <?php echo $service['preparation_time']; ?>min
                                    </span>
                                <?php endif; ?>
                                <span class="timing-item main">
                                    <i class="fas fa-cut"></i> Service: <?php echo $service['duration']; ?>min
                                </span>
                                <?php if ($service['cleanup_time'] > 0): ?>
                                    <span class="timing-item cleanup">
                                        <i class="fas fa-broom"></i> Cleanup: <?php echo $service['cleanup_time']; ?>min
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="service-features">
                        <?php if ($service['employee_selection'] === 'auto'): ?>
                            <span class="feature-badge auto">
                                <i class="fas fa-magic"></i> Auto-assign
                            </span>
                        <?php endif; ?>

                        <span class="feature-badge popularity <?php echo $popularity; ?>">
                            <i class="fas fa-star"></i> <?php echo ucfirst($popularity); ?>
                        </span>
                    </div>

                    <div class="entity-actions">
                        <button class="btn btn-sm btn-outline" onclick="viewService('<?php echo $service['id']; ?>')">
                            <i class="fas fa-eye"></i> View
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editService('<?php echo $service['id']; ?>')">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="btn btn-sm btn-success" onclick="duplicateService('<?php echo $service['id']; ?>')">
                            <i class="fas fa-copy"></i> Duplicate
                        </button>
                    </div>
                </div>

                <div class="entity-footer">
                    Created: <?php echo date('M j, Y', strtotime($service['created_at'])); ?>
                    <?php if (!$service['is_active']): ?>
                        <span class="status-inactive">• Inactive</span>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; ?>

        <!-- Add Service Card -->
        <div class="entity-card add-card" onclick="addService()">
            <div class="entity-card-body" style="display: flex; align-items: center; justify-content: center; min-height: 200px; flex-direction: column; cursor: pointer;">
                <i class="fas fa-plus fa-3x text-muted" style="margin-bottom: 16px;"></i>
                <h3 style="color: var(--text-muted); margin: 0;">Add New Service</h3>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if ($pagination->getTotalPages() > 1): ?>
    <div class="pagination-container">
        <?php echo $pagination->render(); ?>
    </div>
<?php endif; ?>

<style>
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.page-actions {
    display: flex;
    gap: 10px;
}

.card-title-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.category-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.pagination-container {
    margin-top: 30px;
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .page-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
}
</style>
