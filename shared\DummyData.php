<?php

/**
 * Dummy Data Generator
 * Generates test data for tenant databases
 */

class DummyData
{
    private array $categories = [
        ['name' => 'Κομμωτήριο', 'name_en' => 'Hair Salon', 'description' => 'Υπηρεσίες κομμωτηρίου για άνδρες και γυναίκες', 'icon' => '✂️', 'color' => '#FF6B6B'],
        ['name' => 'Αισθητική', 'name_en' => 'Beauty', 'description' => 'Υπηρεσίες αισθητικής και περιποίησης προσώπου', 'icon' => '💄', 'color' => '#4ECDC4'],
        ['name' => 'Μασάζ', 'name_en' => 'Massage', 'description' => 'Θεραπευτικά και χαλαρωτικά μασάζ', 'icon' => '💆', 'color' => '#45B7D1'],
        ['name' => 'Νύχια', 'name_en' => 'Nails', 'description' => 'Υπηρεσίες περιποίησης νυχιών', 'icon' => '💅', 'color' => '#96CEB4']
    ];

    private array $services = [
        // Hair services
        ['category' => 'Κομμωτήριο', 'name' => 'Κούρεμα Ανδρικό', 'name_en' => 'Men\'s Haircut', 'description' => 'Κλασικό ανδρικό κούρεμα με ψαλίδι και μηχανή', 'duration' => 30, 'price' => 15.00],
        ['category' => 'Κομμωτήριο', 'name' => 'Κούρεμα Γυναικείο', 'name_en' => 'Women\'s Haircut', 'description' => 'Κούρεμα και styling για γυναίκες', 'duration' => 45, 'price' => 25.00],
        ['category' => 'Κομμωτήριο', 'name' => 'Βαφή Μαλλιών', 'name_en' => 'Hair Coloring', 'description' => 'Βαφή μαλλιών με επαγγελματικά προϊόντα', 'duration' => 120, 'price' => 45.00],

        // Beauty services
        ['category' => 'Αισθητική', 'name' => 'Καθαρισμός Προσώπου', 'name_en' => 'Facial Cleansing', 'description' => 'Βαθύς καθαρισμός προσώπου με ατμό και εξαγωγή κομεδόνων', 'duration' => 60, 'price' => 35.00],
        ['category' => 'Αισθητική', 'name' => 'Μάσκα Προσώπου', 'name_en' => 'Face Mask', 'description' => 'Ενυδατική μάσκα προσώπου για όλους τους τύπους δέρματος', 'duration' => 45, 'price' => 25.00],
        ['category' => 'Αισθητική', 'name' => 'Αποτρίχωση Ποδιών', 'name_en' => 'Leg Waxing', 'description' => 'Αποτρίχωση ποδιών με κερί για μακροχρόνια αποτελέσματα', 'duration' => 45, 'price' => 20.00],

        // Massage services
        ['category' => 'Μασάζ', 'name' => 'Μασάζ Χαλάρωσης', 'name_en' => 'Relaxation Massage', 'description' => 'Χαλαρωτικό μασάζ ολόκληρου του σώματος', 'duration' => 60, 'price' => 40.00],
        ['category' => 'Μασάζ', 'name' => 'Μασάζ Αθλητικό', 'name_en' => 'Sports Massage', 'description' => 'Θεραπευτικό μασάζ για αθλητές και ενεργούς ανθρώπους', 'duration' => 45, 'price' => 35.00],

        // Nail services
        ['category' => 'Νύχια', 'name' => 'Μανικιούρ', 'name_en' => 'Manicure', 'description' => 'Περιποίηση νυχιών χεριών με βερνίκι', 'duration' => 45, 'price' => 15.00],
        ['category' => 'Νύχια', 'name' => 'Πεντικιούρ', 'name_en' => 'Pedicure', 'description' => 'Περιποίηση νυχιών ποδιών με βερνίκι', 'duration' => 60, 'price' => 20.00]
    ];

    private array $employees = [
        [
            'name' => 'Μαρία Παπαδοπούλου',
            'name_en' => 'Maria Papadopoulou',
            'email' => '<EMAIL>',
            'phone' => '6901234567',
            'position' => 'Senior Hair Stylist',
            'color' => '#FF6B6B',
            'specialization' => 'hair',
            'working_hours' => [
                'monday' => [['start' => '09:00', 'end' => '17:00']],
                'tuesday' => [['start' => '09:00', 'end' => '17:00']],
                'wednesday' => [['start' => '09:00', 'end' => '17:00']],
                'thursday' => [['start' => '09:00', 'end' => '17:00']],
                'friday' => [['start' => '09:00', 'end' => '17:00']],
                'saturday' => [['start' => '09:00', 'end' => '15:00']]
            ]
        ],
        [
            'name' => 'Γιάννης Κωνσταντίνου',
            'name_en' => 'Yannis Konstantinou',
            'email' => '<EMAIL>',
            'phone' => '6902345678',
            'position' => 'Hair Colorist & Barber',
            'color' => '#4ECDC4',
            'specialization' => 'hair',
            'working_hours' => [
                'tuesday' => [['start' => '10:00', 'end' => '18:00']],
                'wednesday' => [['start' => '10:00', 'end' => '18:00']],
                'thursday' => [['start' => '10:00', 'end' => '18:00']],
                'friday' => [['start' => '10:00', 'end' => '18:00']],
                'saturday' => [['start' => '09:00', 'end' => '17:00']]
            ]
        ],
        [
            'name' => 'Ελένη Γεωργίου',
            'name_en' => 'Eleni Georgiou',
            'email' => '<EMAIL>',
            'phone' => '6903456789',
            'position' => 'Nail Technician',
            'color' => '#45B7D1',
            'specialization' => 'nails',
            'working_hours' => [
                'monday' => [['start' => '11:00', 'end' => '19:00']],
                'tuesday' => [['start' => '11:00', 'end' => '19:00']],
                'wednesday' => [['start' => '11:00', 'end' => '19:00']],
                'thursday' => [['start' => '11:00', 'end' => '19:00']],
                'friday' => [['start' => '11:00', 'end' => '19:00']]
            ]
        ],
        [
            'name' => 'Σοφία Αντωνίου',
            'name_en' => 'Sofia Antoniou',
            'email' => '<EMAIL>',
            'phone' => '6904567890',
            'position' => 'Beauty Specialist',
            'color' => '#96CEB4',
            'specialization' => 'beauty',
            'working_hours' => [
                'monday' => [['start' => '08:00', 'end' => '14:00']],
                'wednesday' => [['start' => '08:00', 'end' => '14:00']],
                'friday' => [['start' => '08:00', 'end' => '14:00']],
                'saturday' => [['start' => '10:00', 'end' => '16:00']]
            ]
        ]
    ];

    private array $customers = [
        ['name' => 'Κατερίνα Αλεξίου', 'email' => '<EMAIL>', 'phone' => '6911111111'],
        ['name' => 'Γιάννης Νικολάου', 'email' => '<EMAIL>', 'phone' => '6922222222'],
        ['name' => 'Δέσποινα Μιχαήλ', 'email' => '<EMAIL>', 'phone' => '6933333333'],
        ['name' => 'Νίκος Παπαγιάννης', 'email' => '<EMAIL>', 'phone' => '6944444444'],
        ['name' => 'Χριστίνα Βασιλείου', 'email' => '<EMAIL>', 'phone' => '6955555555'],
        ['name' => 'Δημήτρης Κωστόπουλος', 'email' => '<EMAIL>', 'phone' => '6966666666']
    ];

    public function generateCategories(Database $db): int
    {
        $count = 0;
        foreach ($this->categories as $category) {
            $db->query(
                "INSERT OR IGNORE INTO categories (id, name, name_en, description, icon, color, is_active, created_at)
                 VALUES (:id, :name, :name_en, :description, :icon, :color, 1, :created)",
                [
                    ':id' => 'CAT' . strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8)),
                    ':name' => $category['name'],
                    ':name_en' => $category['name_en'],
                    ':description' => $category['description'],
                    ':icon' => $category['icon'],
                    ':color' => $category['color'],
                    ':created' => date('Y-m-d H:i:s')
                ]
            );
            $count++;
        }
        return $count;
    }

    public function generateServices(Database $db): int
    {
        
        $count = 0;
        foreach ($this->services as $service) {
            // Get category ID
            $category = $db->fetchRow(
                "SELECT id FROM categories WHERE name = :name",
                [':name' => $service['category']]
            );
            
            if ($category) {
                $db->query(
                    "INSERT OR IGNORE INTO services (id, category_id, name, name_en, description, duration, price, employee_selection, preparation_time, cleanup_time, is_active, created_at)
                     VALUES (:id, :category_id, :name, :name_en, :description, :duration, :price, :employee_selection, :preparation_time, :cleanup_time, 1, :created)",
                    [
                        ':id' => 'SRV' . strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8)),
                        ':category_id' => $category['id'],
                        ':name' => $service['name'],
                        ':name_en' => $service['name_en'],
                        ':description' => $service['description'],
                        ':duration' => $service['duration'],
                        ':price' => $service['price'],
                        ':employee_selection' => 'auto',
                        ':preparation_time' => 0,
                        ':cleanup_time' => 0,
                        ':created' => date('Y-m-d H:i:s')
                    ]
                );
                $count++;
            }
        }
        return $count;
    }

    public function generateEmployees(Database $db): int
    {
        $count = 0;

        foreach ($this->employees as $employee) {
            $employeeId = 'EMP' . strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));

            // Use employee-specific working hours
            $workingHours = $employee['working_hours'];

            // Create employee
            $db->query(
                "INSERT OR IGNORE INTO employees (id, name, name_en, email, phone, position, color, working_hours, is_active, created_at)
                 VALUES (:id, :name, :name_en, :email, :phone, :position, :color, :working_hours, 1, :created)",
                [
                    ':id' => $employeeId,
                    ':name' => $employee['name'],
                    ':name_en' => $employee['name_en'],
                    ':email' => $employee['email'],
                    ':phone' => $employee['phone'],
                    ':position' => $employee['position'],
                    ':color' => $employee['color'],
                    ':working_hours' => json_encode($workingHours),
                    ':created' => date('Y-m-d H:i:s')
                ]
            );
            $count++;
        }
        return $count;
    }

    public function generateCustomers(Database $db): int
    {
        $count = 0;
        foreach ($this->customers as $customer) {
            $db->query(
                "INSERT OR IGNORE INTO customers (id, name, email, phone, language, created_at)
                 VALUES (:id, :name, :email, :phone, :language, :created)",
                [
                    ':id' => 'CUS' . strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8)),
                    ':name' => $customer['name'],
                    ':email' => $customer['email'],
                    ':phone' => $customer['phone'],
                    ':language' => 'el',
                    ':created' => date('Y-m-d H:i:s')
                ]
            );
            $count++;
        }
        return $count;
    }

    public function generateReservations(Database $db): int
    {
        $services = $db->fetchAll("SELECT * FROM services WHERE is_active = 1");
        $employees = $db->fetchAll("SELECT id FROM employees WHERE is_active = 1");
        $customers = $db->fetchAll("SELECT id FROM customers");

        if (empty($services) || empty($employees) || empty($customers)) {
            return 0;
        }

        $count = 0;
        $statuses = ['confirmed', 'completed', 'cancelled'];

        // Generate reservations for the past 7 days and next 7 days
        for ($i = -7; $i <= 7; $i++) {
            $date = date('Y-m-d', strtotime("$i days"));

            // Generate 0-3 reservations per day
            $dailyReservations = rand(0, 3);
            $attempts = 0;
            $maxAttempts = 20; // Prevent infinite loops

            for ($j = 0; $j < $dailyReservations && $attempts < $maxAttempts; $j++) {
                $attempts++;

                $service = $services[array_rand($services)];
                $employee = $employees[array_rand($employees)];
                $customer = $customers[array_rand($customers)];

                $hour = rand(9, 16); // 9 AM to 4 PM (to allow for service duration)
                $minute = rand(0, 3) * 15; // 0, 15, 30, 45 minutes
                $startTime = sprintf('%02d:%02d', $hour, $minute);

                // Simple conflict check - just check if employee has another reservation at same time
                if ($i >= 0) {
                    $conflict = $db->fetchRow(
                        "SELECT 1 FROM reservations
                         WHERE employee_id = :emp_id
                         AND date = :date
                         AND start_time = :time
                         AND status != 'cancelled'",
                        [
                            ':emp_id' => $employee['id'],
                            ':date' => $date,
                            ':time' => $startTime
                        ]
                    );

                    if ($conflict) {
                        $j--; // Try again with different time/employee
                        continue;
                    }
                }

                // Calculate end time based on service duration
                $endDateTime = new DateTime($date . ' ' . $startTime);
                $endDateTime->add(new DateInterval('PT' . $service['duration'] . 'M'));
                $endTime = $endDateTime->format('H:i');

                $status = $i < 0 ? $statuses[array_rand($statuses)] : 'confirmed';

                try {
                    $db->query(
                        "INSERT INTO reservations (id, customer_id, service_id, employee_id, date, start_time, end_time, status, price, created_at)
                         VALUES (:id, :customer_id, :service_id, :employee_id, :date, :start_time, :end_time, :status, :price, :created)",
                        [
                            ':id' => 'RSV' . strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8)),
                            ':customer_id' => $customer['id'],
                            ':service_id' => $service['id'],
                            ':employee_id' => $employee['id'],
                            ':date' => $date,
                            ':start_time' => $startTime,
                            ':end_time' => $endTime,
                            ':status' => $status,
                            ':price' => $service['price'],
                            ':created' => date('Y-m-d H:i:s')
                        ]
                    );
                    $count++;
                } catch (Exception $e) {
                    // Skip this reservation if it conflicts (database constraint)
                    error_log("Dummy reservation conflict: " . $e->getMessage());
                    $j--; // Try again
                }
            }
        }

        return $count;
    }

    public function generateAll(Database $db): array
    {
        $results = [];
        $results['categories'] = $this->generateCategories($db);
        $results['services'] = $this->generateServices($db);
        $results['employees'] = $this->generateEmployees($db);
        $results['employee_services'] = $this->generateEmployeeServices($db);
        $results['customers'] = $this->generateCustomers($db);
        $results['reservations'] = $this->generateReservations($db);

        return $results;
    }

    public function generateEmployeeServices(Database $db): int
    {
        $count = 0;

        // Get all employees and services with categories
        $employees = $db->fetchAll("SELECT id, name FROM employees WHERE is_active = 1");
        $services = $db->fetchAll("
            SELECT s.id, s.name, c.name as category_name
            FROM services s
            LEFT JOIN categories c ON s.category_id = c.id
            WHERE s.is_active = 1
        ");

        if (empty($employees) || empty($services)) {
            return 0;
        }

        // Create realistic employee-service assignments based on specializations
        $employeeSpecializations = [];
        $employeeIndex = 0;
        foreach ($this->employees as $empData) {
            if ($employeeIndex < count($employees)) {
                $employeeSpecializations[$employees[$employeeIndex]['id']] = $empData['specialization'];
                $employeeIndex++;
            }
        }

        foreach ($employees as $employee) {
            $specialization = $employeeSpecializations[$employee['id']] ?? 'general';

            foreach ($services as $service) {
                $shouldAssign = false;

                // Assign services based on specialization
                switch ($specialization) {
                    case 'hair':
                        $shouldAssign = stripos($service['category_name'], 'Κομμωτήριο') !== false ||
                                       stripos($service['name'], 'Κούρεμα') !== false ||
                                       stripos($service['name'], 'Βαφή') !== false;
                        break;
                    case 'nails':
                        $shouldAssign = stripos($service['category_name'], 'Νύχια') !== false ||
                                       stripos($service['name'], 'Μανικιούρ') !== false ||
                                       stripos($service['name'], 'Πεντικιούρ') !== false;
                        break;
                    case 'beauty':
                        $shouldAssign = stripos($service['category_name'], 'Αισθητική') !== false ||
                                       stripos($service['name'], 'Καθαρισμός') !== false ||
                                       stripos($service['name'], 'Μάσκα') !== false ||
                                       stripos($service['name'], 'Αποτρίχωση') !== false;
                        break;
                    default:
                        // General employees can do basic services
                        $shouldAssign = stripos($service['category_name'], 'Μασάζ') !== false;
                        break;
                }

                if ($shouldAssign) {
                    $db->query(
                        "INSERT OR IGNORE INTO employee_services (employee_id, service_id) VALUES (:emp_id, :svc_id)",
                        [
                            ':emp_id' => $employee['id'],
                            ':svc_id' => $service['id']
                        ]
                    );
                    $count++;
                }
            }
        }

        return $count;
    }
}
