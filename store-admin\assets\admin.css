/* Store Admin CSS - Enhanced Modern Design System */
:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --primary-light: #dbeafe;
  --primary-gradient: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  --secondary-color: #1e293b;
  --secondary-light: #f1f5f9;
  --success-color: #10b981;
  --success-light: #d1fae5;
  --warning-color: #f59e0b;
  --warning-light: #fef3c7;
  --danger-color: #ef4444;
  --danger-light: #fee2e2;
  --info-color: #06b6d4;
  --info-light: #cffafe;
  --light-color: #f8fafc;
  --dark-color: #0f172a;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --text-color: #1e293b;
  --text-muted: #64748b;
  --text-light: #94a3b8;
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 80px;
  --header-height: 72px;
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background: var(--light-color);
  font-weight: 400;
  font-size: 14px;
  overflow-x: hidden;
}

/* Enhanced Layout System */
.admin-container {
  display: flex;
  min-height: 100vh;
  position: relative;
}

.sidebar {
  width: var(--sidebar-width);
  background: white;
  border-right: 1px solid var(--border-color);
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: var(--z-fixed);
  transition: var(--transition);
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: calc(var(--z-fixed) - 1);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}
  height: 100vh;
  overflow-y: auto;
  z-index: 1000;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-lg);
}

/* Enhanced Sidebar */
.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 12px;
  background: var(--primary-gradient);
  color: white;
}

.sidebar-header h1 {
  font-size: 20px;
  font-weight: 700;
  color: white;
  white-space: nowrap;
  transition: var(--transition);
}

.sidebar.collapsed .sidebar-header h1 {
  opacity: 0;
  width: 0;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-radius);
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar-nav {
  padding: 16px 0;
}

.nav-section {
  margin-bottom: 24px;
}

.nav-section-title {
  padding: 0 20px 8px;
  font-size: 12px;
  font-weight: 600;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: var(--transition);
}

.sidebar.collapsed .nav-section-title {
  opacity: 0;
  height: 0;
  padding: 0;
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: var(--text-color);
  text-decoration: none;
  transition: var(--transition);
  position: relative;
  border-radius: 0;
}

.nav-link:hover {
  background: var(--secondary-light);
  color: var(--primary-color);
}

.nav-link.active {
  background: var(--primary-light);
  color: var(--primary-color);
  font-weight: 600;
}

.nav-link.active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--primary-color);
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  stroke-width: 1.5;
}

.nav-text {
  font-weight: 500;
  white-space: nowrap;
  transition: var(--transition);
}

.sidebar.collapsed .nav-text {
  opacity: 0;
  width: 0;
}

.nav-badge {
  background: var(--danger-color);
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: auto;
  transition: var(--transition);
  min-width: 18px;
  text-align: center;
}

.sidebar.collapsed .nav-badge {
  opacity: 0;
  width: 0;
}

.sidebar-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 20px;
  border-top: 1px solid var(--border-color);
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--danger-color);
  color: white;
  text-decoration: none;
  border-radius: var(--border-radius);
  text-align: center;
  transition: var(--transition);
  font-weight: 500;
}

.logout-btn:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

/* Enhanced Main Content */
.main-content {
  margin-left: var(--sidebar-width);
  width: calc(100% - var(--sidebar-width));
  min-height: 100vh;
  transition: var(--transition);
  background: var(--light-color);
}

.sidebar.collapsed + .main-content {
  margin-left: var(--sidebar-collapsed-width);
  width: calc(100% - var(--sidebar-collapsed-width));
}

.admin-header {
  background: white;
  padding: 20px 32px;
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.admin-header h1 {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-muted);
  font-size: 14px;
}

.breadcrumb-separator {
  color: var(--border-color);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-search {
  position: relative;
  width: 320px;
}

.search-input {
  width: 100%;
  padding: 10px 16px 10px 44px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: var(--transition);
  background: var(--light-color);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  background: white;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  width: 16px;
  height: 16px;
}

.admin-user {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: var(--light-color);
  border-radius: var(--border-radius-lg);
  transition: var(--transition);
}

.admin-user:hover {
  background: var(--secondary-light);
}

/* Enhanced Page Content */
.page-content {
  padding: 32px;
  background: var(--light-color);
  min-height: calc(100vh - var(--header-height));
}

/* Toolbar */
.toolbar {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 20px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
  box-shadow: var(--shadow-sm);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-bar {
  position: relative;
  min-width: 300px;
}

.search-bar input {
  width: 100%;
  padding: 10px 16px 10px 44px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: var(--transition);
}

.search-bar input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-bar .search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  width: 16px;
  height: 16px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
}

.view-toggle {
  display: flex;
  background: var(--light-color);
  border-radius: var(--border-radius);
  padding: 4px;
}

.view-toggle button {
  padding: 8px 12px;
  border: none;
  background: none;
  color: var(--text-muted);
  border-radius: calc(var(--border-radius) - 2px);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 6px;
}

.view-toggle button.active {
  background: white;
  color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

/* Enhanced Entity Grids */
.entity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.entity-card {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: 0;
  transition: var(--transition);
  overflow: hidden;
  position: relative;
}

.entity-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
  border-color: var(--primary-color);
}

.entity-card.selected {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.entity-card-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.entity-select {
  position: absolute;
  top: 16px;
  left: 16px;
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.entity-status-indicator {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: var(--shadow-sm);
}

.entity-status-indicator.active {
  background: var(--success-color);
}

.entity-status-indicator.inactive {
  background: var(--text-muted);
}

.entity-status-indicator.warning {
  background: var(--warning-color);
}

.entity-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 40px 8px 40px;
  line-height: 1.3;
}

.entity-subtitle {
  color: var(--text-muted);
  font-size: 14px;
  margin: 0 40px;
}

.entity-card-body {
  padding: 20px 24px;
}

.entity-info {
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-label {
  color: var(--text-muted);
  font-weight: 500;
}

.info-value {
  color: var(--text-color);
  font-weight: 500;
  text-align: right;
}

.entity-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 16px;
  margin: 20px 0;
  padding: 20px 0;
  border-top: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 4px;
}

.stat-label {
  color: var(--text-muted);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.entity-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  flex-wrap: wrap;
}

.entity-footer {
  padding: 16px 24px;
  background: var(--light-color);
  border-top: 1px solid var(--border-color);
  font-size: 12px;
  color: var(--text-muted);
}

/* Bulk Actions */
.bulk-actions {
  display: none;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  background: var(--primary-color);
  color: white;
  border-radius: var(--border-radius);
  margin-bottom: 16px;
  box-shadow: var(--shadow-md);
}

.bulk-actions.show {
  display: flex;
}

.bulk-actions-text {
  font-weight: 500;
}

.bulk-actions-buttons {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

/* Modern Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 6px 12px;
  border: none;
  border-radius: var(--border-radius);
  text-decoration: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: var(--transition);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--text-muted);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: #4a5568;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-success {
  background: var(--success-color);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-success:hover {
  background: #38a169;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-warning {
  background: var(--warning-color);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-warning:hover {
  background: #dd6b20;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-danger {
  background: var(--danger-color);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-danger:hover {
  background: #e53e3e;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-info {
  background: var(--info-color);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-info:hover {
  background: #3182ce;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-block {
  width: 100%;
}

.btn-icon {
  padding: 8px;
  background: none;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-icon:hover {
  background: var(--light-color);
}

.btn-icon.btn-edit {
  color: var(--primary-color);
}

.btn-icon.btn-delete {
  color: var(--danger-color);
}

.btn-icon.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modern Cards */
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

.card {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 40px 24px 0 24px;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: var(--transition);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: rgba(102, 126, 234, 0.2);
}

.card:hover::before {
  opacity: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.card-title-section {
  flex-direction: column;
  align-items: flex-start !important;
}

.card-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--secondary-color);
  display: flex;
  gap: 0.5em;
}

.card-actions {
  display: flex;
  gap: 5px;
  flex-direction: column;
}

.card-field {
  margin-bottom: 10px;
  display: flex;
  gap: 0.5em;
}

.field-label {
  font-weight: 500;
  color: var(--text-muted);
}

.field-value {
  margin-left: 10px;
  color: var(--text-color);
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-active {
  background: #d4edda;
  color: #155724;
}

.status-inactive {
  background: #f8d7da;
  color: #721c24;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-confirmed {
  background: #d1ecf1;
  color: #0c5460;
}

.status-cancelled {
  background: #f5c6cb;
  color: #721c24;
}

.status-completed {
  background: #d4edda;
  color: #155724;
}

/* Forms */
.form-container {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  gap: 20px;
}

.form-row .form-group {
  flex: 1;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--text-color);
}

input[type="text"],
input[type="email"],
input[type="tel"],
input[type="number"],
input[type="date"],
input[type="time"],
input[type="password"],
input[type="url"],
select,
textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  font-family: inherit;
  transition: var(--transition);
  background: white;
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

textarea {
  resize: vertical;
  min-height: 100px;
}

/* Search and Filter */
.search-filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  gap: 20px;
}

.search-section {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

#search-button {
  white-space: nowrap;
}

/* Card Date Display */
.card-date {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 0.8em;
  color: #999;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 3px;
}

/* Clickable Stats */
.clickable-stat {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: bold;
  cursor: pointer;
}

.clickable-stat:hover {
  text-decoration: underline;
  color: var(--primary-dark);
}

/* Enhanced Accordion System */
.accordion-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 5px;
  transition: all 0.3s ease;
}

.accordion-header:hover {
  background: #e9ecef;
  border-color: #dee2e6;
}

.accordion-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.accordion-count {
  background: var(--primary-color);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: 500;
}

.accordion-arrow {
  transition: transform 0.3s ease;
  color: #666;
  font-size: 0.9em;
}

.accordion-arrow.rotated {
  transform: rotate(180deg);
}

.accordion-content {
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease;
  max-height: 500px;
  opacity: 1;
}

.accordion-content.collapsed {
  max-height: 0;
  opacity: 0;
  margin: 0;
  padding: 0;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 8px;
  padding: 10px 0;
}

.working-schedule {
  padding: 10px 0;
}

.schedule-item {
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 4px;
  font-size: 0.9em;
}

/* Duration Display */
.duration-display {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  font-size: 1.1em;
}

.duration-prep {
  color: #856404;
  background: #fff3cd;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.9em;
}

.duration-main {
  color: #155724;
  background: #d4edda;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 1.1em;
  font-weight: bold;
}

.duration-cleanup {
  color: #0c5460;
  background: #d1ecf1;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.9em;
}

/* Auto Selection Badge */
.auto-selection-badge {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: 500;
  margin: 5px 0;
}

.auto-selection-badge i {
  font-size: 0.9em;
}

/* Enhanced Price Display */
.price-display {
  display: flex;
  align-items: baseline;
  gap: 2px;
  margin: 10px 0;
}

.price-value {
  font-size: 1.8em;
  font-weight: bold;
  color: #2e7d32;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.price-currency {
  font-size: 1.2em;
  color: #666;
  font-weight: 500;
}

/* Contact Fields with Icons */
.contact-field {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;
  padding: 6px 0;
}

.contact-icon {
  color: #666;
  width: 16px;
  text-align: center;
}

.contact-value {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.contact-value:hover {
  text-decoration: underline;
}

/* Language Field */
.language-field {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;
  padding: 6px 0;
}

.language-icon {
  color: #666;
  width: 16px;
  text-align: center;
}

.language-value {
  background: #f8f9fa;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.9em;
  color: #495057;
}

/* Category Services Grid */
.category-services-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
  padding: 10px 0;
}

.category-service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid var(--primary-color);
}

.service-name {
  font-weight: 500;
  color: #333;
}

.service-price {
  font-weight: bold;
  color: #2e7d32;
  font-size: 0.9em;
}

.service-inactive {
  background: #f8d7da;
  color: #721c24;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.7em;
  text-transform: uppercase;
}

/* Enhanced Status Badges */
.badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8em;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.badge-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.badge-warning {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.badge-info {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.badge-pending {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.badge-confirmed {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.badge-completed {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.badge-cancelled {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Card Title Enhancement */
.card-title {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 8px 12px;
  border-radius: 6px;
  margin-bottom: 5px;
  border-left: 4px solid var(--primary-color);
}

.card-header {
  position: relative;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-input-group {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-input-group i {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
}

.search-input {
  padding-left: 35px;
}

.per-page-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.per-page-select {
  width: auto;
  min-width: 80px;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.pagination a,
.pagination span {
  display: inline-block;
  padding: 8px 12px;
  margin: 0 2px;
  text-decoration: none;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  transition: all 0.3s;
}

.pagination a:hover {
  background: var(--primary-color);
  color: white;
}

.pagination .current {
  background: var(--primary-color);
  color: white;
}

/* Flash Messages */
.flash-message {
  padding: 15px 20px;
  margin-bottom: 20px;
  border-radius: 4px;
  font-weight: 500;
}

.flash-success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.flash-error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.flash-warning {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.flash-info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* Modern Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-content {
  background: white;
  border-radius: var(--border-radius-lg);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
  animation: slideIn 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--secondary-color);
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-muted);
  transition: var(--transition);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: var(--danger-color);
  background: rgba(245, 101, 101, 0.1);
  transform: scale(1.1);
}

.modal-body {
  padding: 30px;
}

/* Dashboard Statistics */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stat-card i {
  font-size: 3rem;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--secondary-color);
}

.stat-label {
  font-size: 1rem;
  color: var(--text-muted);
  margin-top: 5px;
}

/* Tables */
.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.table th {
  background: var(--light-color);
  font-weight: 600;
  color: var(--secondary-color);
}

.table tr:hover {
  background: #f8f9fa;
}

/* Login Page */
.login-page {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-container {
  width: 100%;
  max-width: 400px;
  padding: 20px;
}

.login-card {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h1 {
  font-size: 2rem;
  color: var(--secondary-color);
  margin-bottom: 10px;
}

.login-header p {
  color: var(--text-muted);
}

.login-form {
  margin-bottom: 20px;
}

.input-group {
  position: relative;
}

.input-group i {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
}

.input-group input {
  padding-left: 35px;
}

.password-toggle {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-muted);
  transition: color 0.3s;
}

.password-toggle:hover {
  color: var(--primary-color);
}

.login-footer {
  text-align: center;
  color: var(--text-muted);
  font-size: 0.9rem;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

.error-message i {
  margin-right: 5px;
}

/* Legacy responsive styles removed - using enhanced responsive design below */

  .modal-content {
    width: 95%;
    margin: 10px;
  }

  .modal-header,
  .modal-body {
    padding: 20px;
  }

  .search-filter-bar {
    flex-direction: column;
    gap: 15px;
  }

  .search-input-group {
    max-width: 100%;
  }

  .form-row {
    flex-direction: column;
  }
}

/* Utility Classes */
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-left {
  text-align: left;
}
.text-muted {
  color: var(--text-muted);
}
.text-primary {
  color: var(--primary-color);
}
.text-success {
  color: var(--success-color);
}
.text-warning {
  color: var(--warning-color);
}
.text-danger {
  color: var(--danger-color);
}
.text-info {
  color: var(--info-color);
}

.mb-0 {
  margin-bottom: 0;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 1rem;
}
.mb-4 {
  margin-bottom: 1.5rem;
}
.mb-5 {
  margin-bottom: 3rem;
}

.mt-0 {
  margin-top: 0;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-3 {
  margin-top: 1rem;
}
.mt-4 {
  margin-top: 1.5rem;
}
.mt-5 {
  margin-top: 3rem;
}

.d-none {
  display: none;
}
.d-block {
  display: block;
}
.d-inline {
  display: inline;
}
.d-inline-block {
  display: inline-block;
}
.d-flex {
  display: flex;
}

.justify-content-center {
  justify-content: center;
}
.justify-content-between {
  justify-content: space-between;
}
.align-items-center {
  align-items: center;
}
.flex-wrap {
  flex-wrap: wrap;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 1rem;
}

/* Enhanced Mobile-First Responsive Design */
@media (max-width: 1200px) {
  .entity-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
    z-index: var(--z-modal);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
  }

  .main-content {
    margin-left: 0;
    width: 100%;
  }

  .entity-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .entity-stats {
    grid-template-columns: repeat(3, 1fr);
  }

  .admin-header {
    padding: 16px 24px;
  }
}

@media (max-width: 768px) {
  .page-content {
    padding: 16px;
  }

  .admin-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    height: auto;
    padding: 16px 20px;
  }

  .header-search {
    width: 100%;
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    padding: 16px;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: stretch;
    flex-wrap: wrap;
  }

  .search-bar {
    min-width: auto;
    width: 100%;
  }

  .entity-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .entity-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .entity-actions {
    flex-direction: column;
    gap: 8px;
  }

  .bulk-actions {
    flex-direction: column;
    align-items: stretch;
    padding: 16px;
  }

  .bulk-actions-buttons {
    margin-left: 0;
    margin-top: 12px;
    flex-wrap: wrap;
  }

  .view-toggle {
    width: 100%;
  }

  .view-toggle button {
    flex: 1;
  }

  .modal-content {
    width: 95%;
    margin: 20px 10px;
  }

  .modal-header,
  .modal-body {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .page-content {
    padding: 12px;
  }

  .entity-card-header,
  .entity-card-body {
    padding: 16px;
  }

  .entity-stats {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .admin-header h1 {
    font-size: 24px;
  }

  .btn {
    padding: 12px 16px;
    font-size: 14px;
  }

  .btn-sm {
    padding: 8px 12px;
    font-size: 12px;
  }

  .toolbar {
    padding: 12px;
  }

  .entity-title {
    font-size: 16px;
    margin: 0 32px 8px 32px;
  }

  .bulk-actions-buttons {
    flex-direction: column;
    gap: 8px;
  }
}

/* Entity-Specific Styles */

/* Customer Cards */
.customer-card .contact-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.customer-card .contact-link:hover {
  text-decoration: underline;
}

/* Service Cards */
.service-card .category-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.service-card .service-timing {
  margin: 16px 0;
  padding: 12px;
  background: var(--light-color);
  border-radius: var(--border-radius);
}

.service-card .timing-breakdown {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.service-card .timing-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-muted);
}

.service-card .timing-item.prep {
  color: var(--warning-color);
}

.service-card .timing-item.main {
  color: var(--primary-color);
  font-weight: 600;
}

.service-card .timing-item.cleanup {
  color: var(--info-color);
}

.service-card .service-features {
  display: flex;
  gap: 8px;
  margin: 12px 0;
  flex-wrap: wrap;
}

.service-card .feature-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: 11px;
  font-weight: 500;
}

.service-card .feature-badge.auto {
  background: var(--info-light);
  color: var(--info-color);
}

.service-card .feature-badge.popularity.basic {
  background: var(--secondary-light);
  color: var(--text-muted);
}

.service-card .feature-badge.popularity.standard {
  background: var(--warning-light);
  color: var(--warning-color);
}

.service-card .feature-badge.popularity.premium {
  background: var(--success-light);
  color: var(--success-color);
}

/* Employee Cards */
.employee-card .employee-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
  margin: 0 auto 12px;
  box-shadow: var(--shadow-sm);
}

.employee-card .employee-skills {
  margin: 16px 0;
}

.employee-card .skills-header {
  margin-bottom: 8px;
}

.employee-card .skills-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.employee-card .skills-list {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.employee-card .skill-badge {
  display: inline-block;
  padding: 2px 6px;
  background: var(--primary-light);
  color: var(--primary-color);
  border-radius: var(--border-radius);
  font-size: 11px;
  font-weight: 500;
}

.employee-card .skill-badge.more {
  background: var(--secondary-light);
  color: var(--text-muted);
}

/* Reservation Cards */
.reservation-card .reservation-datetime {
  margin-bottom: 16px;
  padding: 12px;
  background: var(--light-color);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
}

.reservation-card.confirmed .reservation-datetime {
  border-left-color: var(--success-color);
}

.reservation-card.completed .reservation-datetime {
  border-left-color: var(--info-color);
}

.reservation-card.cancelled .reservation-datetime {
  border-left-color: var(--danger-color);
}

.reservation-card.pending .reservation-datetime {
  border-left-color: var(--warning-color);
}

.reservation-card .datetime-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reservation-card .date-info,
.reservation-card .time-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.reservation-card .date-value.today {
  color: var(--warning-color);
  font-weight: 600;
}

.reservation-card .date-value.past {
  color: var(--text-muted);
}

.reservation-card .date-value.future {
  color: var(--success-color);
}

.reservation-card .time-value {
  font-weight: 600;
  color: var(--text-color);
}

.reservation-card .time-separator {
  color: var(--text-muted);
  margin: 0 4px;
}

.reservation-card .employee-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: var(--border-radius);
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.reservation-card .status-badge {
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.reservation-card .status-confirmed {
  background: var(--success-light);
  color: var(--success-color);
}

.reservation-card .status-completed {
  background: var(--info-light);
  color: var(--info-color);
}

.reservation-card .status-cancelled {
  background: var(--danger-light);
  color: var(--danger-color);
}

.reservation-card .status-pending {
  background: var(--warning-light);
  color: var(--warning-color);
}

.reservation-card .has-notes {
  color: var(--info-color);
  font-size: 12px;
}

/* Add Card Styles */
.add-card {
  border: 2px dashed var(--border-color);
  background: var(--light-color);
  transition: var(--transition);
}

.add-card:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
  transform: translateY(-2px);
}

.add-card:hover h3 {
  color: var(--primary-color) !important;
}

.add-card:hover i {
  color: var(--primary-color) !important;
}

/* Category Cards */
.category-card .category-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  margin: 0 auto 12px;
  box-shadow: var(--shadow-sm);
}

.category-card .category-services {
  margin: 16px 0;
}

.category-card .services-header {
  margin-bottom: 8px;
}

.category-card .services-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.category-card .services-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.category-card .service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: var(--light-color);
  border-radius: var(--border-radius);
  font-size: 12px;
}

.category-card .service-item.more {
  background: var(--secondary-light);
  color: var(--text-muted);
  font-style: italic;
}

.category-card .service-name {
  font-weight: 500;
  color: var(--text-color);
}

.category-card .service-price {
  font-weight: 600;
  color: var(--primary-color);
}
