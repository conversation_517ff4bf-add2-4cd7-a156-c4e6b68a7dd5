<?php

/**
 * Database Management System
 * Handles multi-tenant SQLite databases
 */

class Database
{
    private ?PDO $pdo;
    private string $dbPath;
    private static array $instances = [];
    
    private function __construct(string $dbPath)
    {
        $this->dbPath = $dbPath;
        $this->connect();
    }
    
    public static function getInstance(string $tenant = 'system'): Database
    {
        if (!isset(self::$instances[$tenant])) {
            $dbPath = self::getDbPath($tenant);
            self::$instances[$tenant] = new self($dbPath);
        }

        return self::$instances[$tenant];
    }

    public static function closeInstance(string $tenant): void
    {
        if (isset(self::$instances[$tenant])) {
            self::$instances[$tenant]->pdo = null;
            unset(self::$instances[$tenant]);
        }
    }

    public static function closeAllInstances(): void
    {
        foreach (self::$instances as $tenant => $instance) {
            $instance->pdo = null;
        }
        self::$instances = [];
    }

    private function ensureConnection(): void
    {
        if ($this->pdo === null) {
            $this->connect();
        }
    }
    
    public static function getDbPath(string $tenant): string
    {
        $dataDir = __DIR__ . '/../data';
        
        if ($tenant === 'system') {
            return $dataDir . '/system/system.db';
        }
        
        return $dataDir . '/tenants/' . $tenant . '.db';
    }
    
    private function connect(): void
    {
        $dir = dirname($this->dbPath);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        $isNewDb = !file_exists($this->dbPath);
        
        $this->pdo = new PDO('sqlite:' . $this->dbPath);
        $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $this->pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $this->pdo->setAttribute(PDO::ATTR_TIMEOUT, 30);

        // Enable foreign keys and WAL mode for better concurrency
        $this->pdo->exec('PRAGMA foreign_keys = ON');
        $this->pdo->exec('PRAGMA journal_mode = WAL');
        $this->pdo->exec('PRAGMA synchronous = NORMAL');
        $this->pdo->exec('PRAGMA busy_timeout = 30000');
        
        if ($isNewDb) {
            $this->createSchema();
        } else {
            $this->updateSchema();
        }
    }
    
    private function createSchema(): void
    {
        $schema = $this->getSchema();

        foreach ($schema as $table => $sql) {
            $this->pdo->exec($sql);
        }
    }

    private function updateSchema(): void
    {
        // Add missing columns to existing tables
        $this->addColumnIfNotExists('categories', 'name_en', 'TEXT');
        $this->addColumnIfNotExists('categories', 'icon', 'TEXT');
        $this->addColumnIfNotExists('categories', 'color', 'TEXT');
        $this->addColumnIfNotExists('services', 'name_en', 'TEXT');
        $this->addColumnIfNotExists('employees', 'name_en', 'TEXT');

        // Add missing tables
        $this->createTableIfNotExists('verification_codes');

        // Migrate texts table to new schema
        $this->migrateTextsTable();

        // Migrate admin credentials to settings table
        $this->migrateAdminCredentials();

        // Migrate reservations table to new schema
        // $this->migrateReservationsTable();

        // Migrate services table to new schema
        // $this->migrateServicesTable();

        // Migrate categories table to new schema
        // $this->migrateCategoriesTable();

        // Migrate employees table to new schema
        // $this->migrateEmployeesTable();

        // Migrate customers table to new schema
        // $this->migrateCustomersTable();

        // Migrate other tables to use TEXT IDs
        $this->migrateTableIds();

        // Create conflict prevention constraints and indexes
        $this->createConflictPreventionConstraints();
    }

    private function addColumnIfNotExists(string $table, string $column, string $type): void
    {
        try {
            // Check if column exists
            $result = $this->pdo->query("PRAGMA table_info($table)");
            $columns = $result->fetchAll(PDO::FETCH_ASSOC);

            $columnExists = false;
            foreach ($columns as $col) {
                if ($col['name'] === $column) {
                    $columnExists = true;
                    break;
                }
            }

            if (!$columnExists) {
                $this->pdo->exec("ALTER TABLE $table ADD COLUMN $column $type");
            }
        } catch (Exception $e) {
            // Ignore errors - column might already exist or table might not exist
        }
    }

    private function createTableIfNotExists(string $tableName): void
    {
        $schema = $this->getSchema();
        if (isset($schema[$tableName])) {
            try {
                $this->pdo->exec($schema[$tableName]);
            } catch (PDOException $e) {
                // Table already exists or other error - ignore
            }
        }
    }

    private function migrateTextsTable(): void
    {
        try {
            // Check if texts table exists and has old schema
            $result = $this->pdo->query("PRAGMA table_info(texts)");
            $columns = $result->fetchAll(PDO::FETCH_ASSOC);

            $hasOldSchema = false;
            $hasNewSchema = false;

            foreach ($columns as $col) {
                if ($col['name'] === 'key') {
                    $hasOldSchema = true;
                }
                if ($col['name'] === 'text_key') {
                    $hasNewSchema = true;
                }
            }

            // If table has old schema but not new schema, migrate it
            if ($hasOldSchema && !$hasNewSchema) {
                // Get existing data
                $existingTexts = $this->pdo->query("SELECT * FROM texts")->fetchAll(PDO::FETCH_ASSOC);

                // Rename old table
                $this->pdo->exec("ALTER TABLE texts RENAME TO texts_old");

                // Create new table with new schema
                $schema = $this->getSchema();
                $this->pdo->exec($schema['texts']);

                // Migrate data
                foreach ($existingTexts as $text) {
                    $this->pdo->prepare("
                        INSERT INTO texts (id, text_key, text_value, language, category, created_at, updated_at)
                        VALUES (?, ?, ?, ?, 'ui', ?, ?)
                    ")->execute([
                        'TXT' . uniqid(),
                        $text['key'],
                        $text['value'],
                        $text['language'],
                        $text['updated_at'] ?? date('Y-m-d H:i:s'),
                        $text['updated_at'] ?? date('Y-m-d H:i:s')
                    ]);
                }

                // Drop old table
                $this->pdo->exec("DROP TABLE texts_old");
            }
        } catch (Exception $e) {
            // Migration failed - ignore and continue
        }
    }

    private function migrateAdminCredentials(): void
    {
        try {
            // Check if settings already have admin credentials
            $existingUsername = $this->fetchRow("SELECT value FROM settings WHERE key = 'admin_username'");
            $existingPassword = $this->fetchRow("SELECT value FROM settings WHERE key = 'admin_password'");

            if ($existingUsername && $existingPassword) {
                return; // Already migrated
            }

            // Get admin user from admin_users table
            $adminUser = $this->fetchRow("SELECT username, password_hash FROM admin_users LIMIT 1");

            if (!$adminUser) {
                return; // No admin user to migrate
            }

            // Migrate credentials to settings table
            if (!$existingUsername) {
                $this->query(
                    "INSERT INTO settings (key, value) VALUES ('admin_username', :username)",
                    [':username' => $adminUser['username']]
                );
            }

            if (!$existingPassword) {
                $this->query(
                    "INSERT INTO settings (key, value) VALUES ('admin_password', :password)",
                    [':password' => $adminUser['password_hash']]
                );
            }
        } catch (Exception $e) {
            // Migration failed - ignore and continue
        }
    }

    private function migrateReservationsTable(): void
    {
        try {
            // Check if reservations table exists and has old schema
            $result = $this->pdo->query("PRAGMA table_info(reservations)");
            $columns = $result->fetchAll(PDO::FETCH_ASSOC);

            $hasOldSchema = false;
            $hasNewSchema = false;

            foreach ($columns as $col) {
                if ($col['name'] === 'appointment_date') {
                    $hasOldSchema = true;
                }
                if ($col['name'] === 'date') {
                    $hasNewSchema = true;
                }
            }

            // If table has old schema but not new schema, migrate it
            if ($hasOldSchema && !$hasNewSchema) {
                // Get existing data
                $existingReservations = $this->pdo->query("SELECT * FROM reservations")->fetchAll(PDO::FETCH_ASSOC);

                // Rename old table
                $this->pdo->exec("ALTER TABLE reservations RENAME TO reservations_old");

                // Create new table with new schema
                $schema = $this->getSchema();
                $this->pdo->exec($schema['reservations']);

                // Migrate data
                foreach ($existingReservations as $reservation) {
                    $this->pdo->prepare("
                        INSERT INTO reservations (id, customer_id, service_id, employee_id, date, start_time,
                        status, verification_code, verification_expires, is_verified, notes, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ")->execute([
                        'RSV' . uniqid(),
                        $reservation['customer_id'],
                        $reservation['service_id'],
                        $reservation['employee_id'],
                        $reservation['appointment_date'],
                        $reservation['appointment_time'],
                        $reservation['status'] ?? 'confirmed',
                        $reservation['verification_code'] ?? null,
                        $reservation['verification_expires'] ?? null,
                        $reservation['is_verified'] ?? 0,
                        $reservation['notes'] ?? null,
                        $reservation['created_at'] ?? date('Y-m-d H:i:s'),
                        $reservation['updated_at'] ?? date('Y-m-d H:i:s')
                    ]);
                }

                // Drop old table
                $this->pdo->exec("DROP TABLE reservations_old");
            }
        } catch (Exception $e) {
            // Migration failed - ignore and continue
        }
    }

    private function migrateServicesTable(): void
    {
        try {
            // Check if services table exists and has old schema
            $result = $this->pdo->query("PRAGMA table_info(services)");
            $columns = $result->fetchAll(PDO::FETCH_ASSOC);

            $hasOldSchema = false;
            $hasNewColumns = false;

            foreach ($columns as $col) {
                if ($col['name'] === 'id' && strpos($col['type'], 'INTEGER') !== false) {
                    $hasOldSchema = true;
                }
                if ($col['name'] === 'employee_selection') {
                    $hasNewColumns = true;
                }
            }

            // If table has old schema but not new columns, migrate it
            if ($hasOldSchema && !$hasNewColumns) {
                // Get existing data
                $existingServices = $this->pdo->query("SELECT * FROM services")->fetchAll(PDO::FETCH_ASSOC);

                // Rename old table
                $this->pdo->exec("ALTER TABLE services RENAME TO services_old");

                // Create new table with new schema
                $schema = $this->getSchema();
                $this->pdo->exec($schema['services']);

                // Migrate data
                foreach ($existingServices as $service) {
                    $this->pdo->prepare("
                        INSERT INTO services (id, category_id, name, name_en, description, duration, price,
                        employee_selection, preparation_time, cleanup_time, buffer_time, is_active, sort_order, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ")->execute([
                        'SRV' . uniqid(),
                        'CAT' . $service['category_id'], // Convert to TEXT ID
                        $service['name'],
                        $service['name_en'] ?? null,
                        $service['description'] ?? null,
                        $service['duration'],
                        $service['price'],
                        'auto', // Default employee_selection
                        0, // Default preparation_time
                        0, // Default cleanup_time
                        $service['buffer_time'] ?? 0,
                        $service['is_active'] ?? 1,
                        $service['sort_order'] ?? 0,
                        $service['created_at'] ?? date('Y-m-d H:i:s'),
                        $service['updated_at'] ?? date('Y-m-d H:i:s')
                    ]);
                }

                // Drop old table
                $this->pdo->exec("DROP TABLE services_old");
            }
        } catch (Exception $e) {
            // Migration failed - ignore and continue
        }
    }

    private function migrateTableIds(): void
    {
        try {
            // Add missing columns to existing tables
            $this->addColumnIfNotExists('services', 'employee_selection', 'TEXT DEFAULT \'auto\'');
            $this->addColumnIfNotExists('services', 'preparation_time', 'INTEGER DEFAULT 0');
            $this->addColumnIfNotExists('services', 'cleanup_time', 'INTEGER DEFAULT 0');

            $this->addColumnIfNotExists('employees', 'position', 'TEXT');
            $this->addColumnIfNotExists('employees', 'color', 'TEXT');
            $this->addColumnIfNotExists('employees', 'working_hours', 'TEXT');

            $this->addColumnIfNotExists('customers', 'language', 'TEXT DEFAULT \'el\'');
            $this->addColumnIfNotExists('customers', 'notes', 'TEXT');
        } catch (Exception $e) {
            // Migration failed - ignore and continue
        }
    }

    /**
     * Create indexes and constraints to prevent time conflicts
     */
    private function createConflictPreventionConstraints(): void
    {
        try {
            // Performance indexes for reservation queries
            $this->pdo->exec("CREATE INDEX IF NOT EXISTS idx_reservations_date_status
                             ON reservations(date, status)");

            $this->pdo->exec("CREATE INDEX IF NOT EXISTS idx_reservations_employee_date
                             ON reservations(employee_id, date, status)");

            $this->pdo->exec("CREATE INDEX IF NOT EXISTS idx_reservations_service_date
                             ON reservations(service_id, date, status)");

            $this->pdo->exec("CREATE INDEX IF NOT EXISTS idx_reservations_datetime
                             ON reservations(date, start_time, end_time)");

            // Conflict prevention: Prevent same employee being double-booked
            // Note: SQLite doesn't support partial unique indexes with WHERE clause in all versions
            // So we'll handle this in application logic with proper locking

            error_log("Conflict prevention constraints created successfully");

        } catch (Exception $e) {
            error_log("Failed to create conflict prevention constraints: " . $e->getMessage());
            // Don't throw - this is not critical for basic functionality
        }
    }

    private function migrateCategoriesTable(): void
    {
        try {
            // Check if categories table exists first
            $tableExists = $this->pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='categories'")->fetch();
            if (!$tableExists) {
                return;
            }

            // Check if categories table has INTEGER ID
            $result = $this->pdo->query("PRAGMA table_info(categories)");
            $columns = $result->fetchAll(PDO::FETCH_ASSOC);

            $hasIntegerId = false;
            foreach ($columns as $col) {
                if ($col['name'] === 'id' && strpos($col['type'], 'INTEGER') !== false) {
                    $hasIntegerId = true;
                    break;
                }
            }

            if ($hasIntegerId) {
                // Get existing data
                $existingCategories = $this->pdo->query("SELECT * FROM categories")->fetchAll(PDO::FETCH_ASSOC);

                // Rename old table
                $this->pdo->exec("ALTER TABLE categories RENAME TO categories_old");

                // Create new table with new schema
                $schema = $this->getSchema();
                $this->pdo->exec($schema['categories']);

                // Migrate data
                foreach ($existingCategories as $category) {
                    $this->pdo->prepare("
                        INSERT INTO categories (id, name, name_en, description, icon, color, sort_order, is_active, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ")->execute([
                        'CAT' . uniqid(),
                        $category['name'],
                        $category['name_en'] ?? null,
                        $category['description'] ?? null,
                        $category['icon'] ?? null,
                        $category['color'] ?? null,
                        $category['sort_order'] ?? 0,
                        $category['is_active'] ?? 1,
                        $category['created_at'] ?? date('Y-m-d H:i:s'),
                        $category['updated_at'] ?? date('Y-m-d H:i:s')
                    ]);
                }

                // Drop old table
                $this->pdo->exec("DROP TABLE categories_old");
            }
        } catch (Exception $e) {
            // Migration failed - ignore and continue
        }
    }

    private function migrateEmployeesTable(): void
    {
        try {
            // Check if employees table exists first
            $tableExists = $this->pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='employees'")->fetch();
            if (!$tableExists) {
                return;
            }

            // Check if employees table has INTEGER ID
            $result = $this->pdo->query("PRAGMA table_info(employees)");
            $columns = $result->fetchAll(PDO::FETCH_ASSOC);

            $hasIntegerId = false;
            foreach ($columns as $col) {
                if ($col['name'] === 'id' && strpos($col['type'], 'INTEGER') !== false) {
                    $hasIntegerId = true;
                    break;
                }
            }

            if ($hasIntegerId) {
                // Get existing data
                $existingEmployees = $this->pdo->query("SELECT * FROM employees")->fetchAll(PDO::FETCH_ASSOC);

                // Rename old table
                $this->pdo->exec("ALTER TABLE employees RENAME TO employees_old");

                // Create new table with new schema
                $schema = $this->getSchema();
                $this->pdo->exec($schema['employees']);

                // Migrate data
                foreach ($existingEmployees as $employee) {
                    $this->pdo->prepare("
                        INSERT INTO employees (id, name, name_en, email, phone, position, color, working_hours, is_active, sort_order, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ")->execute([
                        'EMP' . uniqid(),
                        $employee['name'],
                        $employee['name_en'] ?? null,
                        $employee['email'] ?? null,
                        $employee['phone'] ?? null,
                        $employee['position'] ?? 'Specialist',
                        $employee['color'] ?? '#' . substr(md5($employee['name']), 0, 6),
                        $employee['working_hours'] ?? null,
                        $employee['is_active'] ?? 1,
                        $employee['sort_order'] ?? 0,
                        $employee['created_at'] ?? date('Y-m-d H:i:s'),
                        $employee['updated_at'] ?? date('Y-m-d H:i:s')
                    ]);
                }

                // Drop old table
                $this->pdo->exec("DROP TABLE employees_old");
            }
        } catch (Exception $e) {
            // Migration failed - ignore and continue
        }
    }

    private function migrateCustomersTable(): void
    {
        try {
            // Check if customers table exists first
            $tableExists = $this->pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='customers'")->fetch();
            if (!$tableExists) {
                return;
            }

            // Check if customers table has INTEGER ID
            $result = $this->pdo->query("PRAGMA table_info(customers)");
            $columns = $result->fetchAll(PDO::FETCH_ASSOC);

            $hasIntegerId = false;
            foreach ($columns as $col) {
                if ($col['name'] === 'id' && strpos($col['type'], 'INTEGER') !== false) {
                    $hasIntegerId = true;
                    break;
                }
            }

            if ($hasIntegerId) {
                // Get existing data
                $existingCustomers = $this->pdo->query("SELECT * FROM customers")->fetchAll(PDO::FETCH_ASSOC);

                // Rename old table
                $this->pdo->exec("ALTER TABLE customers RENAME TO customers_old");

                // Create new table with new schema
                $schema = $this->getSchema();
                $this->pdo->exec($schema['customers']);

                // Migrate data
                foreach ($existingCustomers as $customer) {
                    $this->pdo->prepare("
                        INSERT INTO customers (id, name, email, phone, language, notes, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ")->execute([
                        'CUS' . uniqid(),
                        $customer['name'],
                        $customer['email'] ?? null,
                        $customer['phone'] ?? null,
                        $customer['language'] ?? 'el',
                        $customer['notes'] ?? null,
                        $customer['created_at'] ?? date('Y-m-d H:i:s'),
                        $customer['updated_at'] ?? date('Y-m-d H:i:s')
                    ]);
                }

                // Drop old table
                $this->pdo->exec("DROP TABLE customers_old");
            }
        } catch (Exception $e) {
            // Migration failed - ignore and continue
        }
    }

    private function getSchema(): array
    {
        return [
            'categories' => "
                CREATE TABLE categories (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    name_en TEXT,
                    description TEXT,
                    icon TEXT,
                    color TEXT,
                    sort_order INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ",
            'services' => "
                CREATE TABLE services (
                    id TEXT PRIMARY KEY,
                    category_id TEXT,
                    name TEXT NOT NULL,
                    name_en TEXT,
                    description TEXT,
                    duration INTEGER NOT NULL,
                    price DECIMAL(10,2) NOT NULL,
                    employee_selection TEXT DEFAULT 'auto',
                    preparation_time INTEGER DEFAULT 0,
                    cleanup_time INTEGER DEFAULT 0,
                    buffer_time INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    sort_order INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES categories(id)
                )
            ",
            'employees' => "
                CREATE TABLE employees (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    name_en TEXT,
                    email TEXT,
                    phone TEXT,
                    position TEXT,
                    color TEXT,
                    working_hours TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    sort_order INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ",
            'employee_services' => "
                CREATE TABLE employee_services (
                    employee_id TEXT NOT NULL,
                    service_id TEXT NOT NULL,
                    PRIMARY KEY (employee_id, service_id),
                    FOREIGN KEY (employee_id) REFERENCES employees(id),
                    FOREIGN KEY (service_id) REFERENCES services(id)
                )
            ",
            'customers' => "
                CREATE TABLE customers (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    email TEXT,
                    phone TEXT,
                    language TEXT DEFAULT 'el',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ",
            'reservations' => "
                CREATE TABLE reservations (
                    id TEXT PRIMARY KEY,
                    customer_id TEXT NOT NULL,
                    service_id TEXT NOT NULL,
                    employee_id TEXT,
                    date DATE NOT NULL,
                    start_time TIME NOT NULL,
                    end_time TIME,
                    status TEXT DEFAULT 'confirmed',
                    price DECIMAL(10,2),
                    verification_code TEXT,
                    verification_expires TIMESTAMP,
                    is_verified BOOLEAN DEFAULT 0,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers(id),
                    FOREIGN KEY (service_id) REFERENCES services(id),
                    FOREIGN KEY (employee_id) REFERENCES employees(id)
                )
            ",
            'settings' => "
                CREATE TABLE settings (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ",
            'texts' => "
                CREATE TABLE texts (
                    id TEXT PRIMARY KEY,
                    text_key TEXT NOT NULL,
                    text_value TEXT NOT NULL,
                    description TEXT,
                    language TEXT NOT NULL,
                    category TEXT DEFAULT 'general',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(text_key, language, category)
                )
            ",
            'working_hours' => "
                CREATE TABLE working_hours (
                    day_of_week INTEGER NOT NULL,
                    start_time TIME,
                    end_time TIME,
                    is_active BOOLEAN DEFAULT 1,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (day_of_week)
                )
            ",
            'special_days' => "
                CREATE TABLE special_days (
                    date DATE PRIMARY KEY,
                    is_closed BOOLEAN DEFAULT 0,
                    start_time TIME,
                    end_time TIME,
                    notes TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ",
            'verification_codes' => "
                CREATE TABLE verification_codes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    type TEXT NOT NULL,
                    identifier TEXT NOT NULL,
                    code TEXT NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    used_at TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )",
            'tenants' => "
                CREATE TABLE tenants (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    subdomain TEXT NOT NULL UNIQUE,
                    business_name TEXT NOT NULL,
                    owner_name TEXT,
                    owner_email TEXT,
                    status TEXT DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ",
            'admin_users' => "
                CREATE TABLE admin_users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT NOT NULL UNIQUE,
                    password_hash TEXT NOT NULL,
                    email TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    last_login TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            "
        ];
    }
    
    public function query(string $sql, array $params = []): PDOStatement
    {
        $this->ensureConnection();
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    }
    
    public function fetchAll(string $sql, array $params = []): array
    {
        return $this->query($sql, $params)->fetchAll();
    }
    
    public function fetchRow(string $sql, array $params = []): ?array
    {
        $result = $this->query($sql, $params)->fetch();
        return $result ?: null;
    }
    
    public function fetchColumn(string $sql, array $params = [])
    {
        return $this->query($sql, $params)->fetchColumn();
    }
    
    public function lastInsertId(): string
    {
        $this->ensureConnection();
        return $this->pdo->lastInsertId();
    }

    public function beginTransaction(): bool
    {
        $this->ensureConnection();
        return $this->pdo->beginTransaction();
    }

    public function commit(): bool
    {
        $this->ensureConnection();
        return $this->pdo->commit();
    }

    public function rollback(): bool
    {
        $this->ensureConnection();
        return $this->pdo->rollback();
    }

    public function exec(string $sql)
    {
        $this->ensureConnection();
        return $this->pdo->exec($sql);
    }

    public function getDatabasePath(): string
    {
        return $this->dbPath;
    }

    public function inTransaction(): bool
    {
        $this->ensureConnection();
        return $this->pdo->inTransaction();
    }

    // Static methods for system-admin compatibility
    public static function master(): Database
    {
        return self::getInstance('system');
    }

    public static function tenant(string $tenantId): Database
    {
        return self::getInstance($tenantId);
    }

    public static function tenantSafe(string $tenantId): Database
    {
        // Check if tenant exists before creating database instance
        if ($tenantId !== 'demo' && $tenantId !== 'system') {
            try {
                $systemDb = self::master();
                $result = $systemDb->fetchRow(
                    "SELECT id FROM tenants WHERE subdomain = :subdomain AND status = 'active'",
                    [':subdomain' => $tenantId]
                );
                if (!$result) {
                    throw new Exception("Tenant '$tenantId' does not exist");
                }
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'does not exist') !== false) {
                    throw $e;
                }
                // If we can't check (e.g., system not initialized), allow it
            }
        }
        return self::getInstance($tenantId);
    }
}
