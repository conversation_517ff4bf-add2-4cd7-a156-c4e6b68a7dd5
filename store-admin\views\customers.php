<?php
/**
 * Customers View
 * Manage customers
 */

// Get search and pagination parameters
$searchParams = AdminHelpers::getSearchParams();
$search = $searchParams['search'];
$pageNum = $searchParams['page_num'];
$perPage = $searchParams['per_page'];

// Build search query - search by name, email, phone, and notes
$searchWhere = AdminHelpers::buildSearchWhere($search, ['name', 'email', 'phone', 'notes']);

// Get total count for pagination first
$countSql = "SELECT COUNT(*) as total FROM customers c {$searchWhere['where']}";
$totalCount = $db->fetchRow($countSql, $searchWhere['params'])['total'];

// Create pagination
$pagination = new Pagination($totalCount, $perPage, $pageNum);

// Get customers with reservation count using proper SQL pagination (simplified to avoid schema issues)
$sql = "SELECT c.*, COUNT(r.id) as reservation_count,
               MAX(r.created_at) as last_reservation_date
        FROM customers c
        LEFT JOIN reservations r ON c.id = r.customer_id
        {$searchWhere['where']}
        GROUP BY c.id
        ORDER BY c.name ASC
        LIMIT {$perPage} OFFSET {$pagination->getOffset()}";

try {
    $paginatedCustomers = $db->fetchAll($sql, $searchWhere['params']);
} catch (Exception $e) {
    // If reservations table has schema issues, just get customers without reservation data
    $sql = "SELECT c.*, 0 as reservation_count, NULL as last_reservation_date
            FROM customers c
            {$searchWhere['where']}
            ORDER BY c.name ASC
            LIMIT {$perPage} OFFSET {$pagination->getOffset()}";
    $paginatedCustomers = $db->fetchAll($sql, $searchWhere['params']);
}
?>

<!-- Enhanced Toolbar -->
<div class="toolbar">
    <div class="toolbar-left">
        <div class="search-bar">
            <input type="text" placeholder="Search by name, email, phone..." value="<?php echo htmlspecialchars($search); ?>" id="customer-search">
            <i class="fas fa-search search-icon"></i>
        </div>
        <div class="filter-group">
            <span class="filter-label">Status:</span>
            <select class="form-control" id="status-filter">
                <option value="">All Customers</option>
                <option value="active">Active</option>
                <option value="vip">VIP</option>
                <option value="new">New</option>
            </select>
        </div>
        <div class="filter-group">
            <span class="filter-label">Last Visit:</span>
            <select class="form-control" id="visit-filter">
                <option value="">All Time</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
            </select>
        </div>
    </div>
    <div class="toolbar-right">
        <div class="view-toggle">
            <button class="active" data-view="cards">
                <i class="fas fa-th-large"></i> Cards
            </button>
            <button data-view="table">
                <i class="fas fa-list"></i> Table
            </button>
        </div>
        <button class="btn btn-primary" onclick="addCustomer()">
            <i class="fas fa-plus"></i> Add Customer
        </button>
    </div>
</div>

<!-- Bulk Actions -->
<div class="bulk-actions" id="bulk-actions">
    <span class="bulk-actions-text">
        <span id="selected-count">0</span> customers selected
    </span>
    <div class="bulk-actions-buttons">
        <button class="btn btn-secondary btn-sm" onclick="bulkEmail()">
            <i class="fas fa-envelope"></i> Email
        </button>
        <button class="btn btn-secondary btn-sm" onclick="bulkSMS()">
            <i class="fas fa-sms"></i> SMS
        </button>
        <button class="btn btn-secondary btn-sm" onclick="bulkExport()">
            <i class="fas fa-download"></i> Export
        </button>
        <button class="btn btn-danger btn-sm" onclick="bulkDelete()">
            <i class="fas fa-trash"></i> Delete
        </button>
    </div>
</div>

<!-- Enhanced Customers Grid -->
<div class="entity-grid" id="customers-grid">
    <?php if (empty($paginatedCustomers)): ?>
        <div class="empty-state">
            <i class="fas fa-users fa-4x text-muted"></i>
            <h3>No customers found</h3>
            <p>Start by adding your first customer or adjust your search criteria.</p>
            <button class="btn btn-primary btn-lg" onclick="addCustomer()">
                <i class="fas fa-plus"></i> Add First Customer
            </button>
        </div>
    <?php else: ?>
        <?php foreach ($paginatedCustomers as $customer): ?>
            <?php
            $reservationCount = $customer['reservation_count'] ?? 0;
            $lastVisit = $customer['last_reservation_date'] ?? null;
            $customerType = $reservationCount > 10 ? 'vip' : ($reservationCount > 3 ? 'regular' : 'new');
            ?>
            <div class="entity-card customer-card" data-id="<?php echo $customer['id']; ?>">
                <div class="entity-card-header">
                    <input type="checkbox" class="entity-select" value="<?php echo $customer['id']; ?>">
                    <div class="entity-status-indicator <?php echo $customerType === 'vip' ? 'active' : ($customerType === 'regular' ? 'warning' : 'inactive'); ?>"></div>
                    <h3 class="entity-title"><?php echo htmlspecialchars($customer['name']); ?></h3>
                    <div class="entity-subtitle">
                        <?php echo ucfirst($customerType); ?> Customer
                    </div>
                </div>

                <div class="entity-card-body">
                    <div class="entity-info">
                        <?php if (!empty($customer['email'])): ?>
                            <div class="info-row">
                                <span class="info-label">
                                    <i class="fas fa-envelope"></i> Email
                                </span>
                                <a href="mailto:<?php echo htmlspecialchars($customer['email']); ?>" class="info-value contact-link">
                                    <?php echo htmlspecialchars($customer['email']); ?>
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($customer['phone'])): ?>
                            <div class="info-row">
                                <span class="info-label">
                                    <i class="fas fa-phone"></i> Phone
                                </span>
                                <a href="tel:<?php echo htmlspecialchars($customer['phone']); ?>" class="info-value contact-link">
                                    <?php echo htmlspecialchars($customer['phone']); ?>
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($customer['language'])): ?>
                            <div class="info-row">
                                <span class="info-label">
                                    <i class="fas fa-globe"></i> Language
                                </span>
                                <span class="info-value">
                                    <?php echo strtoupper($customer['language']); ?>
                                </span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="entity-stats">
                        <div class="stat-item">
                            <div class="stat-value"><?php echo $reservationCount; ?></div>
                            <div class="stat-label">Visits</div>
                        </div>
                        <?php if ($lastVisit): ?>
                            <div class="stat-item">
                                <div class="stat-value"><?php echo date('M j', strtotime($lastVisit)); ?></div>
                                <div class="stat-label">Last Visit</div>
                            </div>
                        <?php else: ?>
                            <div class="stat-item">
                                <div class="stat-value">-</div>
                                <div class="stat-label">Last Visit</div>
                            </div>
                        <?php endif; ?>
                        <div class="stat-item">
                            <div class="stat-value">€<?php echo number_format(($reservationCount * 35), 0); ?></div>
                            <div class="stat-label">Est. Value</div>
                        </div>
                    </div>

                    <?php if (!empty($customer['notes'])): ?>
                        <div class="entity-notes">
                            <strong>Notes:</strong>
                            <p><?php echo nl2br(htmlspecialchars(substr($customer['notes'], 0, 100))); ?><?php echo strlen($customer['notes']) > 100 ? '...' : ''; ?></p>
                        </div>
                    <?php endif; ?>

                    <div class="entity-actions">
                        <button class="btn btn-sm btn-outline" onclick="viewCustomer('<?php echo $customer['id']; ?>')">
                            <i class="fas fa-eye"></i> View
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editCustomer('<?php echo $customer['id']; ?>')">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="btn btn-sm btn-success" onclick="bookAppointment('<?php echo $customer['id']; ?>')">
                            <i class="fas fa-calendar-plus"></i> Book
                        </button>
                    </div>
                </div>

                <div class="entity-footer">
                    Created: <?php echo date('M j, Y', strtotime($customer['created_at'])); ?>
                </div>
            </div>
        <?php endforeach; ?>

        <!-- Add Customer Card -->
        <div class="entity-card add-card" onclick="addCustomer()">
            <div class="entity-card-body" style="display: flex; align-items: center; justify-content: center; min-height: 200px; flex-direction: column; cursor: pointer;">
                <i class="fas fa-plus fa-3x text-muted" style="margin-bottom: 16px;"></i>
                <h3 style="color: var(--text-muted); margin: 0;">Add New Customer</h3>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if ($pagination->getTotalPages() > 1): ?>
    <div class="pagination-container">
        <?php echo $pagination->render(); ?>
    </div>
<?php endif; ?>

<style>
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.page-actions {
    display: flex;
    gap: 10px;
}

.card-title-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.customer-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.customer-stats {
    font-size: 0.8rem;
    color: var(--text-muted);
    background: var(--light-color);
    padding: 4px 8px;
    border-radius: 4px;
}

.customer-stats-section {
    background: var(--light-color);
    padding: 15px;
    border-radius: 4px;
    margin: 10px 0;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.stat-item:last-child {
    margin-bottom: 0;
}

.stat-label {
    font-weight: 500;
    color: var(--text-muted);
}

.stat-value {
    font-weight: 600;
    color: var(--secondary-color);
}

.card-footer {
    display: flex;
    gap: 10px;
    padding: 15px 20px;
    background: var(--light-color);
    border-top: 1px solid var(--border-color);
    margin: 15px -20px -20px -20px;
    border-radius: 0 0 8px 8px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
}

.pagination-container {
    margin-top: 30px;
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .page-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .card-footer {
        flex-direction: column;
    }
}
</style>
