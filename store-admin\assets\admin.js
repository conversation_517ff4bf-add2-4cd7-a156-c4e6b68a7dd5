/**
 * Enhanced Store Admin JavaScript
 * Handles all client-side functionality for the modern admin interface
 */

// Global variables
let currentEditId = null;
let currentPage = 1;
let currentSearch = '';
let currentPerPage = 20;

// Enhanced UI Initialization
document.addEventListener('DOMContentLoaded', function() {
    initializeEnhancedUI();
    initializeBulkActions();
    initializeViewToggle();
    initializeEnhancedSearch();
    initializeFilters();
    initializeSidebar();
    initializeTooltips();
    initializeModals();
    initializeFormValidation();
});

// Enhanced UI Components
function initializeEnhancedUI() {
    // Initialize entity selection
    const entitySelects = document.querySelectorAll('.entity-select');
    entitySelects.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (typeof window.updateBulkActions === 'function') {
                window.updateBulkActions();
            }
        });
    });

    // Initialize card interactions
    const entityCards = document.querySelectorAll('.entity-card:not(.add-card)');
    entityCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            if (!this.classList.contains('selected')) {
                this.style.transform = 'translateY(-2px)';
            }
        });

        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('selected')) {
                this.style.transform = 'translateY(0)';
            }
        });
    });
}

// Bulk Actions Management
function initializeBulkActions() {
    // Define updateBulkActions globally first
    if (typeof window.updateBulkActions === 'undefined') {
        window.updateBulkActions = function() {
            const selected = document.querySelectorAll('.entity-select:checked');
            const count = selected.length;
            const bulkActions = document.getElementById('bulk-actions');
            const selectedCount = document.getElementById('selected-count');

            if (selectedCount) {
                selectedCount.textContent = count;
            }

            if (bulkActions) {
                if (count > 0) {
                    bulkActions.classList.add('show');
                } else {
                    bulkActions.classList.remove('show');
                }
            }

            // Update visual state
            document.querySelectorAll('.entity-card').forEach(card => {
                const checkbox = card.querySelector('.entity-select');
                if (checkbox && checkbox.checked) {
                    card.classList.add('selected');
                } else {
                    card.classList.remove('selected');
                }
            });
        };
    }

    // Select all functionality
    if (typeof window.toggleSelectAll === 'undefined') {
        window.toggleSelectAll = function() {
            const checkboxes = document.querySelectorAll('.entity-select');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);

            checkboxes.forEach(checkbox => {
                checkbox.checked = !allChecked;
            });

            if (typeof window.updateBulkActions === 'function') {
                window.updateBulkActions();
            }
        };
    }
}

// View Toggle
function initializeViewToggle() {
    const viewToggleButtons = document.querySelectorAll('.view-toggle button');

    viewToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            viewToggleButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            const viewType = this.dataset.view;
            toggleView(viewType);
        });
    });
}

function toggleView(viewType) {
    const grid = document.querySelector('.entity-grid');
    const existingTable = document.querySelector('.entity-table');

    if (grid) {
        if (viewType === 'table') {
            grid.style.display = 'none';
            showTableView();
        } else {
            grid.style.display = 'grid';
            hideTableView();
        }

        // Store preference
        localStorage.setItem('admin-view-preference', viewType);
        console.log(`Switched to ${viewType} view`);
    }
}

function showTableView() {
    let table = document.querySelector('.entity-table');

    if (!table) {
        // Create table if it doesn't exist
        table = document.createElement('div');
        table.className = 'entity-table';
        table.innerHTML = generateTableView();

        const grid = document.querySelector('.entity-grid');
        if (grid && grid.parentNode) {
            grid.parentNode.insertBefore(table, grid.nextSibling);
        }
    }

    table.style.display = 'block';
}

function hideTableView() {
    const table = document.querySelector('.entity-table');
    if (table) {
        table.style.display = 'none';
    }
}

function generateTableView() {
    const cards = document.querySelectorAll('.entity-card:not(.add-card)');
    if (cards.length === 0) {
        return '<p>No data to display in table view</p>';
    }

    // Determine entity type from URL or page context
    const urlParams = new URLSearchParams(window.location.search);
    const page = urlParams.get('page') || 'customers';

    let tableHTML = '<div class="table-responsive"><table class="table">';

    // Generate table based on entity type
    switch (page) {
        case 'customers':
            tableHTML += generateCustomersTable(cards);
            break;
        case 'services':
            tableHTML += generateServicesTable(cards);
            break;
        case 'employees':
            tableHTML += generateEmployeesTable(cards);
            break;
        case 'categories':
            tableHTML += generateCategoriesTable(cards);
            break;
        case 'reservations':
            tableHTML += generateReservationsTable(cards);
            break;
        default:
            tableHTML += generateGenericTable(cards);
    }

    tableHTML += '</table></div>';
    return tableHTML;
}

function generateCustomersTable(cards) {
    let html = `
        <thead>
            <tr>
                <th><input type="checkbox" id="select-all-table"></th>
                <th>Name</th>
                <th>Email</th>
                <th>Phone</th>
                <th>Type</th>
                <th>Visits</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
    `;

    cards.forEach(card => {
        const checkbox = card.querySelector('.entity-select');
        const title = card.querySelector('.entity-title')?.textContent || '';
        const subtitle = card.querySelector('.entity-subtitle')?.textContent || '';
        const stats = card.querySelectorAll('.stat-value');
        const id = checkbox?.value || '';

        html += `
            <tr>
                <td><input type="checkbox" class="entity-select" value="${id}"></td>
                <td><strong>${title}</strong></td>
                <td>${getInfoValue(card, 'Email') || '-'}</td>
                <td>${getInfoValue(card, 'Phone') || '-'}</td>
                <td><span class="badge badge-info">${subtitle}</span></td>
                <td>${stats[0]?.textContent || '0'}</td>
                <td>
                    <button class="btn btn-sm btn-outline" onclick="viewCustomer('${id}')">View</button>
                    <button class="btn btn-sm btn-primary" onclick="editCustomer('${id}')">Edit</button>
                </td>
            </tr>
        `;
    });

    html += '</tbody>';
    return html;
}

function generateServicesTable(cards) {
    let html = `
        <thead>
            <tr>
                <th><input type="checkbox" id="select-all-table"></th>
                <th>Service</th>
                <th>Category</th>
                <th>Price</th>
                <th>Duration</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
    `;

    cards.forEach(card => {
        const checkbox = card.querySelector('.entity-select');
        const title = card.querySelector('.entity-title')?.textContent || '';
        const subtitle = card.querySelector('.entity-subtitle')?.textContent || '';
        const stats = card.querySelectorAll('.stat-value');
        const status = card.querySelector('.entity-status-indicator');
        const id = checkbox?.value || '';

        html += `
            <tr>
                <td><input type="checkbox" class="entity-select" value="${id}"></td>
                <td><strong>${title}</strong></td>
                <td><span class="badge badge-info">${subtitle}</span></td>
                <td>€${stats[0]?.textContent || '0'}</td>
                <td>${stats[1]?.textContent || '0'} min</td>
                <td><span class="badge badge-${status?.classList.contains('active') ? 'success' : 'secondary'}">${status?.classList.contains('active') ? 'Active' : 'Inactive'}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline" onclick="viewService('${id}')">View</button>
                    <button class="btn btn-sm btn-primary" onclick="editService('${id}')">Edit</button>
                </td>
            </tr>
        `;
    });

    html += '</tbody>';
    return html;
}

function generateGenericTable(cards) {
    let html = `
        <thead>
            <tr>
                <th><input type="checkbox" id="select-all-table"></th>
                <th>Name</th>
                <th>Details</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
    `;

    cards.forEach(card => {
        const checkbox = card.querySelector('.entity-select');
        const title = card.querySelector('.entity-title')?.textContent || '';
        const subtitle = card.querySelector('.entity-subtitle')?.textContent || '';
        const status = card.querySelector('.entity-status-indicator');
        const id = checkbox?.value || '';

        html += `
            <tr>
                <td><input type="checkbox" class="entity-select" value="${id}"></td>
                <td><strong>${title}</strong></td>
                <td>${subtitle}</td>
                <td><span class="badge badge-${status?.classList.contains('active') ? 'success' : 'secondary'}">${status?.classList.contains('active') ? 'Active' : 'Inactive'}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline">View</button>
                    <button class="btn btn-sm btn-primary">Edit</button>
                </td>
            </tr>
        `;
    });

    html += '</tbody>';
    return html;
}

function getInfoValue(card, label) {
    const infoRows = card.querySelectorAll('.info-row');
    for (let row of infoRows) {
        const labelEl = row.querySelector('.info-label');
        if (labelEl && labelEl.textContent.includes(label)) {
            const valueEl = row.querySelector('.info-value');
            return valueEl ? valueEl.textContent.trim() : '';
        }
    }
    return '';
}

// Missing initialization functions
function initializeEnhancedSearch() {
    const searchInputs = document.querySelectorAll('.search-bar input, #search-input');

    searchInputs.forEach(input => {
        let searchTimeout;

        input.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch(this.value);
            }, 300); // Debounce search
        });

        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                performSearch(this.value);
            }
        });
    });
}

function initializeFilters() {
    const filterSelects = document.querySelectorAll('.filter-group select, .toolbar select');

    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            applyFilters();
        });
    });
}

function initializeModals() {
    // Modal functionality is handled by showModal function
    // This is just a placeholder for any additional modal setup
    console.log('Modals initialized');
}

function initializeFormValidation() {
    // Add form validation listeners
    document.addEventListener('submit', function(e) {
        const form = e.target;
        if (form.classList.contains('entity-form') || form.classList.contains('bulk-form')) {
            if (!validateForm(form)) {
                e.preventDefault();
                return false;
            }
        }
    });
}

function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('error');
            isValid = false;
        } else {
            field.classList.remove('error');
        }
    });

    return isValid;
}

function performSearch(query = '') {
    currentSearch = query;
    console.log('Performing search:', query);

    // In a real implementation, this would make an AJAX call
    // For now, we'll just filter visible cards
    const cards = document.querySelectorAll('.entity-card:not(.add-card)');

    cards.forEach(card => {
        const title = card.querySelector('.entity-title')?.textContent.toLowerCase() || '';
        const subtitle = card.querySelector('.entity-subtitle')?.textContent.toLowerCase() || '';
        const infoValues = Array.from(card.querySelectorAll('.info-value')).map(el => el.textContent.toLowerCase()).join(' ');

        const searchText = `${title} ${subtitle} ${infoValues}`;

        if (!query || searchText.includes(query.toLowerCase())) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function applyFilters() {
    console.log('Applying filters');
    // This would typically make an AJAX call to filter results
    // For now, it's a placeholder
}

// Dynamic Data Loading Functions
function loadCategoriesIntoSelect(selectId) {
    const select = document.getElementById(selectId);
    if (!select) return;

    // Show loading state
    select.innerHTML = '<option value="">Loading categories...</option>';

    // In a real implementation, this would be an AJAX call
    // For now, we'll simulate with mock data or try to get from page
    const categories = getCategoriesFromPage();

    if (categories.length > 0) {
        select.innerHTML = '<option value="">Select Category</option>';
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            select.appendChild(option);
        });
    } else {
        // Fallback: try to fetch via AJAX
        fetchCategories().then(categories => {
            select.innerHTML = '<option value="">Select Category</option>';
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                select.appendChild(option);
            });
        }).catch(error => {
            select.innerHTML = '<option value="">No categories available</option>';
            console.error('Failed to load categories:', error);
        });
    }
}

function loadCustomersIntoSelect(selectId) {
    const select = document.getElementById(selectId);
    if (!select) return;

    select.innerHTML = '<option value="">Loading customers...</option>';

    fetchCustomers().then(customers => {
        select.innerHTML = '<option value="">Select Customer</option>';
        customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = customer.name;
            select.appendChild(option);
        });
    }).catch(error => {
        select.innerHTML = '<option value="">No customers available</option>';
        console.error('Failed to load customers:', error);
    });
}

function loadEmployeesIntoSelect(selectId) {
    const select = document.getElementById(selectId);
    if (!select) return;

    select.innerHTML = '<option value="">Loading employees...</option>';

    fetchEmployees().then(employees => {
        select.innerHTML = '<option value="">Auto-assign</option>';
        employees.forEach(employee => {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = employee.name;
            select.appendChild(option);
        });
    }).catch(error => {
        select.innerHTML = '<option value="">No employees available</option>';
        console.error('Failed to load employees:', error);
    });
}

function loadServicesIntoSelect(selectId) {
    const select = document.getElementById(selectId);
    if (!select) return;

    select.innerHTML = '<option value="">Loading services...</option>';

    fetchServices().then(services => {
        select.innerHTML = '<option value="">Select Service</option>';
        services.forEach(service => {
            const option = document.createElement('option');
            option.value = service.id;
            option.textContent = `${service.name} (€${service.price})`;
            select.appendChild(option);
        });
    }).catch(error => {
        select.innerHTML = '<option value="">No services available</option>';
        console.error('Failed to load services:', error);
    });
}

// Helper functions to get data from current page or fetch via AJAX
function getCategoriesFromPage() {
    const categories = [];
    const categoryCards = document.querySelectorAll('.entity-card:not(.add-card)');

    // Check if we're on categories page
    if (window.location.search.includes('page=categories')) {
        categoryCards.forEach(card => {
            const id = card.querySelector('.entity-select')?.value;
            const name = card.querySelector('.entity-title')?.textContent;
            if (id && name) {
                categories.push({ id, name });
            }
        });
    }

    return categories;
}

async function fetchCategories() {
    try {
        const response = await fetch('/store-admin/controllers/ajax.php?action=list&entity=categories');
        const data = await response.json();
        return data.success ? data.data : [];
    } catch (error) {
        console.error('Error fetching categories:', error);
        return [];
    }
}

async function fetchCustomers() {
    try {
        const response = await fetch('/store-admin/controllers/ajax.php?action=list&entity=customers');
        const data = await response.json();
        return data.success ? data.data : [];
    } catch (error) {
        console.error('Error fetching customers:', error);
        return [];
    }
}

async function fetchEmployees() {
    try {
        const response = await fetch('/store-admin/controllers/ajax.php?action=list&entity=employees');
        const data = await response.json();
        return data.success ? data.data : [];
    } catch (error) {
        console.error('Error fetching employees:', error);
        return [];
    }
}

async function fetchServices() {
    try {
        const response = await fetch('/store-admin/controllers/ajax.php?action=list&entity=services');
        const data = await response.json();
        return data.success ? data.data : [];
    } catch (error) {
        console.error('Error fetching services:', error);
        return [];
    }
}

// Enhanced Search
function initializeEnhancedSearch() {
    const searchInputs = document.querySelectorAll('.search-bar input, .header-search input');

    searchInputs.forEach(input => {
        let searchTimeout;

        input.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performEnhancedSearch(this.value);
            }, 300);
        });

        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                performEnhancedSearch(this.value);
            }
        });
    });
}

function performEnhancedSearch(query) {
    const urlParams = new URLSearchParams(window.location.search);

    if (query.trim()) {
        urlParams.set('search', query);
    } else {
        urlParams.delete('search');
    }

    urlParams.set('page', '1'); // Reset to first page
    window.location.href = window.location.pathname + '?' + urlParams.toString();
}

// Filter Management
function initializeFilters() {
    const filters = document.querySelectorAll('select[id$="-filter"]');

    filters.forEach(filter => {
        filter.addEventListener('change', function() {
            applyFilters();
        });
    });
}

function applyFilters() {
    const urlParams = new URLSearchParams(window.location.search);
    const filters = document.querySelectorAll('select[id$="-filter"]');

    filters.forEach(filter => {
        const paramName = filter.id.replace('-filter', '');
        if (filter.value) {
            urlParams.set(paramName, filter.value);
        } else {
            urlParams.delete(paramName);
        }
    });

    urlParams.set('page', '1'); // Reset to first page
    window.location.href = window.location.pathname + '?' + urlParams.toString();
}

// Enhanced Customer Functions
function addCustomer() {
    showModal('Add Customer', generateCustomerForm(), function(formData) {
        submitForm('customers', 'add', formData);
    });
}

function editCustomer(id) {
    // Fetch customer data and show edit form
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=customers&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Edit Customer', generateCustomerForm(data.data), function(formData) {
                    submitForm('customers', 'edit', formData, id);
                });
            }
        });
}

function viewCustomer(id) {
    // Show customer details in modal
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=customers&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Customer Details', generateCustomerView(data.data));
            }
        });
}

function bookAppointment(customerId) {
    // Redirect to reservations page with customer pre-selected
    window.location.href = `/store-admin/?page=reservations&action=add&customer_id=${customerId}`;
}

function bulkEmail() {
    const selected = getSelectedCustomers();
    if (selected.length === 0) {
        showNotification('Please select customers first', 'warning');
        return;
    }

    showModal('Send Email', generateBulkEmailForm(selected), function(formData) {
        // Handle bulk email
        submitBulkAction('email', selected, formData);
    });
}

function bulkSMS() {
    const selected = getSelectedCustomers();
    if (selected.length === 0) {
        showNotification('Please select customers first', 'warning');
        return;
    }

    showModal('Send SMS', generateBulkSMSForm(selected), function(formData) {
        // Handle bulk SMS
        submitBulkAction('sms', selected, formData);
    });
}

function bulkExport() {
    const selected = getSelectedCustomers();
    if (selected.length === 0) {
        showNotification('Please select customers first', 'warning');
        return;
    }

    // Export selected customers
    const ids = selected.join(',');
    window.open(`/store-admin/controllers/ajax.php?action=export&entity=customers&ids=${ids}`, '_blank');
}

function bulkDelete() {
    const selected = getSelectedCustomers();
    if (selected.length === 0) {
        showNotification('Please select customers first', 'warning');
        return;
    }

    if (confirm(`Are you sure you want to delete ${selected.length} customers? This action cannot be undone.`)) {
        submitBulkAction('delete', selected);
    }
}

function getSelectedCustomers() {
    const checkboxes = document.querySelectorAll('.entity-select:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// Tooltips Initialization
function initializeTooltips() {
    // Create tooltip element if it doesn't exist
    let tooltip = document.getElementById('admin-tooltip');
    if (!tooltip) {
        tooltip = document.createElement('div');
        tooltip.id = 'admin-tooltip';
        tooltip.className = 'tooltip';
        document.body.appendChild(tooltip);
    }

    // Initialize tooltips for elements with title attribute
    const tooltipElements = document.querySelectorAll('[title], [data-tooltip]');

    tooltipElements.forEach(element => {
        const tooltipText = element.getAttribute('title') || element.getAttribute('data-tooltip');
        if (tooltipText) {
            // Remove title to prevent default browser tooltip
            element.removeAttribute('title');
            element.setAttribute('data-tooltip', tooltipText);

            element.addEventListener('mouseenter', function(e) {
                showTooltip(e.target, tooltipText);
            });

            element.addEventListener('mouseleave', function() {
                hideTooltip();
            });
        }
    });
}

function showTooltip(element, text) {
    const tooltip = document.getElementById('admin-tooltip');
    if (!tooltip) return;

    tooltip.textContent = text;
    tooltip.style.display = 'block';

    const rect = element.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();

    // Position tooltip above element
    let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
    let top = rect.top - tooltipRect.height - 8;

    // Adjust if tooltip goes off screen
    if (left < 8) left = 8;
    if (left + tooltipRect.width > window.innerWidth - 8) {
        left = window.innerWidth - tooltipRect.width - 8;
    }
    if (top < 8) {
        top = rect.bottom + 8;
    }

    tooltip.style.left = left + 'px';
    tooltip.style.top = top + 'px';
}

function hideTooltip() {
    const tooltip = document.getElementById('admin-tooltip');
    if (tooltip) {
        tooltip.style.display = 'none';
    }
}

// Enhanced Sidebar Management
function initializeSidebar() {
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    const overlay = document.querySelector('.sidebar-overlay');

    // Initialize sidebar toggle
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Toggle collapsed state on desktop
            if (window.innerWidth > 1024) {
                sidebar.classList.toggle('collapsed');
            } else {
                // Toggle open state on mobile
                sidebar.classList.toggle('open');
                if (overlay) {
                    overlay.classList.toggle('show');
                }
            }
        });
    }

    // Handle overlay clicks on mobile
    if (overlay) {
        overlay.addEventListener('click', function() {
            sidebar.classList.remove('open');
            this.classList.remove('show');
        });
    }

    // Handle responsive behavior
    function handleResize() {
        if (window.innerWidth > 1024) {
            // Desktop: remove mobile classes
            sidebar.classList.remove('open');
            if (overlay) {
                overlay.classList.remove('show');
            }
        } else {
            // Mobile: remove desktop classes
            sidebar.classList.remove('collapsed');
        }
    }

    // Initial setup
    handleResize();
    window.addEventListener('resize', handleResize);

    // Ensure sidebar is properly initialized
    if (sidebar) {
        sidebar.style.display = 'flex';
        sidebar.style.flexDirection = 'column';
    }
}

// Enhanced Service Functions
function addService() {
    showModal('Add Service', generateServiceForm(), function(formData) {
        submitForm('services', 'add', formData);
    });

    // Load categories after modal is shown
    setTimeout(() => {
        loadCategoriesIntoSelect('service-category');
    }, 100);
}

function editService(id) {
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=services&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Edit Service', generateServiceForm(data.data), function(formData) {
                    submitForm('services', 'edit', formData, id);
                });
            }
        });
}

function viewService(id) {
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=services&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Service Details', generateServiceView(data.data));
            }
        });
}

function duplicateService(id) {
    if (confirm('Create a copy of this service?')) {
        fetch(`/store-admin/controllers/ajax.php?action=duplicate&entity=services&id=${id}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Service duplicated successfully', 'success');
                location.reload();
            } else {
                showNotification('Failed to duplicate service', 'error');
            }
        });
    }
}

function manageCategories() {
    showModal('Manage Categories', generateCategoriesManager(), function(formData) {
        // Handle category management
    });
}

function bulkUpdatePrices() {
    const selected = getSelectedServices();
    if (selected.length === 0) {
        showNotification('Please select services first', 'warning');
        return;
    }

    showModal('Update Prices', generateBulkPriceForm(selected), function(formData) {
        submitBulkAction('update_prices', selected, formData);
    });
}

function bulkDuplicate() {
    const selected = getSelectedServices();
    if (selected.length === 0) {
        showNotification('Please select services first', 'warning');
        return;
    }

    if (confirm(`Duplicate ${selected.length} services?`)) {
        submitBulkAction('duplicate', selected);
    }
}

function getSelectedServices() {
    const checkboxes = document.querySelectorAll('.entity-select:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// Enhanced Employee Functions
function addEmployee() {
    showModal('Add Employee', generateEmployeeForm(), function(formData) {
        submitForm('employees', 'add', formData);
    });
}

function editEmployee(id) {
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=employees&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Edit Employee', generateEmployeeForm(data.data), function(formData) {
                    submitForm('employees', 'edit', formData, id);
                });
            }
        });
}

function viewEmployee(id) {
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=employees&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Employee Details', generateEmployeeView(data.data));
            }
        });
}

function manageSchedule(employeeId) {
    window.location.href = `/store-admin/?page=schedules&employee_id=${employeeId}`;
}

function manageSchedules() {
    window.location.href = `/store-admin/?page=schedules`;
}

function bulkSchedule() {
    const selected = getSelectedEmployees();
    if (selected.length === 0) {
        showNotification('Please select employees first', 'warning');
        return;
    }

    showModal('Bulk Schedule', generateBulkScheduleForm(selected), function(formData) {
        submitBulkAction('schedule', selected, formData);
    });
}

function bulkNotify() {
    const selected = getSelectedEmployees();
    if (selected.length === 0) {
        showNotification('Please select employees first', 'warning');
        return;
    }

    showModal('Send Notification', generateBulkNotifyForm(selected), function(formData) {
        submitBulkAction('notify', selected, formData);
    });
}

function getSelectedEmployees() {
    const checkboxes = document.querySelectorAll('.entity-select:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// Enhanced Reservation Functions
function addReservation() {
    showModal('New Reservation', generateReservationForm(), function(formData) {
        submitForm('reservations', 'add', formData);
    });

    // Load dynamic data after modal is shown
    setTimeout(() => {
        loadCustomersIntoSelect('reservation-customer');
        loadServicesIntoSelect('reservation-service');
        loadEmployeesIntoSelect('reservation-employee');
    }, 100);
}

function editReservation(id) {
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=reservations&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Edit Reservation', generateReservationForm(data.data), function(formData) {
                    submitForm('reservations', 'edit', formData, id);
                });
            }
        });
}

function viewReservation(id) {
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=reservations&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Reservation Details', generateReservationView(data.data));
            }
        });
}

function confirmReservation(id) {
    if (confirm('Confirm this reservation?')) {
        updateReservationStatus(id, 'confirmed');
    }
}

function completeReservation(id) {
    if (confirm('Mark this reservation as completed?')) {
        updateReservationStatus(id, 'completed');
    }
}

function updateReservationStatus(id, status) {
    fetch(`/store-admin/controllers/ajax.php?action=update_status&entity=reservations&id=${id}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: status })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Reservation ${status} successfully`, 'success');
            location.reload();
        } else {
            showNotification('Failed to update reservation', 'error');
        }
    });
}

function showCalendar() {
    window.location.href = `/store-admin/?page=calendar`;
}

function bulkConfirm() {
    const selected = getSelectedReservations();
    if (selected.length === 0) {
        showNotification('Please select reservations first', 'warning');
        return;
    }

    if (confirm(`Confirm ${selected.length} reservations?`)) {
        submitBulkAction('confirm', selected);
    }
}

function bulkRemind() {
    const selected = getSelectedReservations();
    if (selected.length === 0) {
        showNotification('Please select reservations first', 'warning');
        return;
    }

    showModal('Send Reminders', generateBulkReminderForm(selected), function(formData) {
        submitBulkAction('remind', selected, formData);
    });
}

function bulkReschedule() {
    const selected = getSelectedReservations();
    if (selected.length === 0) {
        showNotification('Please select reservations first', 'warning');
        return;
    }

    showModal('Bulk Reschedule', generateBulkRescheduleForm(selected), function(formData) {
        submitBulkAction('reschedule', selected, formData);
    });
}

function bulkCancel() {
    const selected = getSelectedReservations();
    if (selected.length === 0) {
        showNotification('Please select reservations first', 'warning');
        return;
    }

    if (confirm(`Cancel ${selected.length} reservations? This action cannot be undone.`)) {
        submitBulkAction('cancel', selected);
    }
}

function getSelectedReservations() {
    const checkboxes = document.querySelectorAll('.entity-select:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// Enhanced Category Functions
function addCategory() {
    showModal('Add Category', generateCategoryForm(), function(formData) {
        submitForm('categories', 'add', formData);
    });
}

function editCategory(id) {
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=categories&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Edit Category', generateCategoryForm(data.data), function(formData) {
                    submitForm('categories', 'edit', formData, id);
                });
            }
        });
}

function viewCategory(id) {
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=categories&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Category Details', generateCategoryView(data.data));
            }
        });
}

function manageServices(categoryId) {
    window.location.href = `/store-admin/?page=services&category_id=${categoryId}`;
}

function reorderCategories() {
    showModal('Reorder Categories', generateReorderForm(), function(formData) {
        submitBulkAction('reorder', [], formData);
    });
}

function bulkReorder() {
    const selected = getSelectedCategories();
    if (selected.length === 0) {
        showNotification('Please select categories first', 'warning');
        return;
    }

    showModal('Bulk Reorder', generateBulkReorderForm(selected), function(formData) {
        submitBulkAction('reorder', selected, formData);
    });
}

function bulkUpdateColors() {
    const selected = getSelectedCategories();
    if (selected.length === 0) {
        showNotification('Please select categories first', 'warning');
        return;
    }

    showModal('Update Colors', generateBulkColorForm(selected), function(formData) {
        submitBulkAction('update_colors', selected, formData);
    });
}

function getSelectedCategories() {
    const checkboxes = document.querySelectorAll('.entity-select:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// Bulk Status Toggle Functions
function bulkToggleStatus() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select items first', 'warning');
        return;
    }

    showModal('Toggle Status', generateBulkStatusForm(selected), function(formData) {
        submitBulkAction('toggle_status', selected, formData);
    });
}

function bulkActivate() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select items first', 'warning');
        return;
    }

    const formData = new FormData();
    formData.append('status', 'active');
    submitBulkAction('toggle_status', selected, formData);
}

function bulkDeactivate() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select items first', 'warning');
        return;
    }

    const formData = new FormData();
    formData.append('status', 'inactive');
    submitBulkAction('toggle_status', selected, formData);
}

function getSelectedItems() {
    const checkboxes = document.querySelectorAll('.entity-select:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

function generateBulkStatusForm(selectedIds) {
    return `
        <form id="bulk-status-form" class="bulk-form">
            <div class="form-group">
                <label for="status-action">Status Action</label>
                <select id="status-action" name="action" required>
                    <option value="activate">Activate All</option>
                    <option value="deactivate">Deactivate All</option>
                    <option value="toggle">Toggle Status</option>
                </select>
            </div>
            <div class="selected-items">
                <p>Updating status for ${selectedIds.length} items</p>
            </div>
        </form>
    `;
}

// Additional Bulk Functions
function bulkDelete() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select items first', 'warning');
        return;
    }

    if (confirm(`Are you sure you want to delete ${selected.length} items? This action cannot be undone.`)) {
        submitBulkAction('delete', selected);
    }
}

function bulkEmail() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select customers first', 'warning');
        return;
    }

    showModal('Send Bulk Email', generateBulkEmailForm(selected), function(formData) {
        submitBulkAction('email', selected, formData);
    });
}

function bulkSMS() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select customers first', 'warning');
        return;
    }

    showModal('Send Bulk SMS', generateBulkSMSForm(selected), function(formData) {
        submitBulkAction('sms', selected, formData);
    });
}

function bulkExport() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select items first', 'warning');
        return;
    }

    // Create export URL
    const exportUrl = `/store-admin/controllers/export.php?ids=${selected.join(',')}&format=csv`;
    window.open(exportUrl, '_blank');
}

function bulkPrint() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select items first', 'warning');
        return;
    }

    // Create print URL
    const printUrl = `/store-admin/controllers/print.php?ids=${selected.join(',')}&format=pdf`;
    window.open(printUrl, '_blank');
}

// Form Generation Functions
function generateCustomerForm(data = null) {
    const isEdit = data !== null;
    return `
        <form id="customer-form" class="entity-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="customer-name">Name *</label>
                    <input type="text" id="customer-name" name="name" value="${data?.name || ''}" required>
                </div>
                <div class="form-group">
                    <label for="customer-email">Email</label>
                    <input type="email" id="customer-email" name="email" value="${data?.email || ''}">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="customer-phone">Phone</label>
                    <input type="tel" id="customer-phone" name="phone" value="${data?.phone || ''}">
                </div>
                <div class="form-group">
                    <label for="customer-language">Language</label>
                    <select id="customer-language" name="language">
                        <option value="en" ${data?.language === 'en' ? 'selected' : ''}>English</option>
                        <option value="el" ${data?.language === 'el' ? 'selected' : ''}>Greek</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label for="customer-notes">Notes</label>
                <textarea id="customer-notes" name="notes" rows="3">${data?.notes || ''}</textarea>
            </div>
        </form>
    `;
}

function generateServiceForm(data = null) {
    const isEdit = data !== null;
    return `
        <form id="service-form" class="entity-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="service-name">Service Name *</label>
                    <input type="text" id="service-name" name="name" value="${data?.name || ''}" required>
                </div>
                <div class="form-group">
                    <label for="service-category">Category</label>
                    <select id="service-category" name="category_id">
                        <option value="">Select Category</option>
                        <!-- Categories will be loaded dynamically -->
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="service-price">Price (€) *</label>
                    <input type="number" id="service-price" name="price" value="${data?.price || ''}" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label for="service-duration">Duration (minutes) *</label>
                    <input type="number" id="service-duration" name="duration" value="${data?.duration || ''}" min="1" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="service-prep-time">Preparation Time (minutes)</label>
                    <input type="number" id="service-prep-time" name="preparation_time" value="${data?.preparation_time || 0}" min="0">
                </div>
                <div class="form-group">
                    <label for="service-cleanup-time">Cleanup Time (minutes)</label>
                    <input type="number" id="service-cleanup-time" name="cleanup_time" value="${data?.cleanup_time || 0}" min="0">
                </div>
            </div>
            <div class="form-group">
                <label for="service-description">Description</label>
                <textarea id="service-description" name="description" rows="3">${data?.description || ''}</textarea>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" name="is_active" ${data?.is_active ? 'checked' : 'checked'}>
                    Active Service
                </label>
            </div>
        </form>
    `;
}

function generateEmployeeForm(data = null) {
    const isEdit = data !== null;
    return `
        <form id="employee-form" class="entity-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="employee-name">Name *</label>
                    <input type="text" id="employee-name" name="name" value="${data?.name || ''}" required>
                </div>
                <div class="form-group">
                    <label for="employee-position">Position</label>
                    <input type="text" id="employee-position" name="position" value="${data?.position || ''}">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="employee-email">Email</label>
                    <input type="email" id="employee-email" name="email" value="${data?.email || ''}">
                </div>
                <div class="form-group">
                    <label for="employee-phone">Phone</label>
                    <input type="tel" id="employee-phone" name="phone" value="${data?.phone || ''}">
                </div>
            </div>
            <div class="form-group">
                <label for="employee-color">Color</label>
                <input type="color" id="employee-color" name="color" value="${data?.color || '#3498db'}">
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" name="is_active" ${data?.is_active ? 'checked' : 'checked'}>
                    Active Employee
                </label>
            </div>
        </form>
    `;
}

function generateCategoryForm(data = null) {
    const isEdit = data !== null;
    return `
        <form id="category-form" class="entity-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="category-name">Category Name *</label>
                    <input type="text" id="category-name" name="name" value="${data?.name || ''}" required>
                </div>
                <div class="form-group">
                    <label for="category-name-en">English Name</label>
                    <input type="text" id="category-name-en" name="name_en" value="${data?.name_en || ''}">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="category-color">Color</label>
                    <input type="color" id="category-color" name="color" value="${data?.color || '#3498db'}">
                </div>
                <div class="form-group">
                    <label for="category-icon">Icon Class</label>
                    <input type="text" id="category-icon" name="icon" value="${data?.icon || ''}" placeholder="fas fa-tag">
                </div>
            </div>
            <div class="form-group">
                <label for="category-description">Description</label>
                <textarea id="category-description" name="description" rows="3">${data?.description || ''}</textarea>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="category-sort-order">Sort Order</label>
                    <input type="number" id="category-sort-order" name="sort_order" value="${data?.sort_order || 0}" min="0">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" name="is_active" ${data?.is_active ? 'checked' : 'checked'}>
                        Active Category
                    </label>
                </div>
            </div>
        </form>
    `;
}

function generateReservationForm(data = null) {
    const isEdit = data !== null;
    const today = new Date().toISOString().split('T')[0];
    return `
        <form id="reservation-form" class="entity-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="reservation-customer">Customer *</label>
                    <select id="reservation-customer" name="customer_id" required>
                        <option value="">Select Customer</option>
                        <!-- Customers will be loaded dynamically -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="reservation-service">Service *</label>
                    <select id="reservation-service" name="service_id" required>
                        <option value="">Select Service</option>
                        <!-- Services will be loaded dynamically -->
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="reservation-date">Date *</label>
                    <input type="date" id="reservation-date" name="date" value="${data?.date || today}" required>
                </div>
                <div class="form-group">
                    <label for="reservation-time">Time *</label>
                    <input type="time" id="reservation-time" name="start_time" value="${data?.start_time || ''}" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="reservation-employee">Employee</label>
                    <select id="reservation-employee" name="employee_id">
                        <option value="">Auto-assign</option>
                        <!-- Employees will be loaded dynamically -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="reservation-status">Status</label>
                    <select id="reservation-status" name="status">
                        <option value="pending" ${data?.status === 'pending' ? 'selected' : ''}>Pending</option>
                        <option value="confirmed" ${data?.status === 'confirmed' ? 'selected' : ''}>Confirmed</option>
                        <option value="completed" ${data?.status === 'completed' ? 'selected' : ''}>Completed</option>
                        <option value="cancelled" ${data?.status === 'cancelled' ? 'selected' : ''}>Cancelled</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label for="reservation-notes">Notes</label>
                <textarea id="reservation-notes" name="notes" rows="3">${data?.notes || ''}</textarea>
            </div>
        </form>
    `;
}

// Manager and View Generation Functions
function generateCategoriesManager() {
    return `
        <div class="categories-manager">
            <h3>Manage Categories</h3>
            <div class="manager-toolbar">
                <button type="button" class="btn btn-primary" onclick="addNewCategory()">
                    <i class="fas fa-plus"></i> Add Category
                </button>
                <button type="button" class="btn btn-secondary" onclick="reorderCategories()">
                    <i class="fas fa-sort"></i> Reorder
                </button>
            </div>
            <div id="categories-list" class="categories-list">
                <p>Loading categories...</p>
            </div>
        </div>
    `;
}

function generateCustomerView(data) {
    return `
        <div class="entity-view customer-view">
            <div class="view-header">
                <h3>${data.name}</h3>
                <div class="view-badges">
                    <span class="badge ${data.reservation_count > 10 ? 'badge-success' : data.reservation_count > 3 ? 'badge-warning' : 'badge-info'}">
                        ${data.reservation_count > 10 ? 'VIP' : data.reservation_count > 3 ? 'Regular' : 'New'} Customer
                    </span>
                </div>
            </div>
            <div class="view-content">
                <div class="info-grid">
                    ${data.email ? `<div class="info-item"><strong>Email:</strong> <a href="mailto:${data.email}">${data.email}</a></div>` : ''}
                    ${data.phone ? `<div class="info-item"><strong>Phone:</strong> <a href="tel:${data.phone}">${data.phone}</a></div>` : ''}
                    ${data.language ? `<div class="info-item"><strong>Language:</strong> ${data.language.toUpperCase()}</div>` : ''}
                    <div class="info-item"><strong>Total Visits:</strong> ${data.reservation_count || 0}</div>
                    ${data.last_reservation_date ? `<div class="info-item"><strong>Last Visit:</strong> ${new Date(data.last_reservation_date).toLocaleDateString()}</div>` : ''}
                    <div class="info-item"><strong>Member Since:</strong> ${new Date(data.created_at).toLocaleDateString()}</div>
                </div>
                ${data.notes ? `<div class="notes-section"><strong>Notes:</strong><p>${data.notes}</p></div>` : ''}
            </div>
        </div>
    `;
}

function generateServiceView(data) {
    return `
        <div class="entity-view service-view">
            <div class="view-header">
                <h3>${data.name}</h3>
                <div class="view-badges">
                    <span class="badge ${data.is_active ? 'badge-success' : 'badge-secondary'}">
                        ${data.is_active ? 'Active' : 'Inactive'}
                    </span>
                    ${data.category_name ? `<span class="badge badge-info">${data.category_name}</span>` : ''}
                </div>
            </div>
            <div class="view-content">
                <div class="info-grid">
                    <div class="info-item"><strong>Price:</strong> €${parseFloat(data.price).toFixed(2)}</div>
                    <div class="info-item"><strong>Duration:</strong> ${data.duration} minutes</div>
                    ${data.preparation_time ? `<div class="info-item"><strong>Preparation:</strong> ${data.preparation_time} minutes</div>` : ''}
                    ${data.cleanup_time ? `<div class="info-item"><strong>Cleanup:</strong> ${data.cleanup_time} minutes</div>` : ''}
                    <div class="info-item"><strong>Total Time:</strong> ${(data.preparation_time || 0) + data.duration + (data.cleanup_time || 0)} minutes</div>
                    <div class="info-item"><strong>Created:</strong> ${new Date(data.created_at).toLocaleDateString()}</div>
                </div>
                ${data.description ? `<div class="description-section"><strong>Description:</strong><p>${data.description}</p></div>` : ''}
            </div>
        </div>
    `;
}

function generateEmployeeView(data) {
    return `
        <div class="entity-view employee-view">
            <div class="view-header">
                <h3>${data.name}</h3>
                <div class="view-badges">
                    <span class="badge ${data.is_active ? 'badge-success' : 'badge-secondary'}">
                        ${data.is_active ? 'Active' : 'Inactive'}
                    </span>
                    ${data.position ? `<span class="badge badge-info">${data.position}</span>` : ''}
                </div>
            </div>
            <div class="view-content">
                <div class="info-grid">
                    ${data.email ? `<div class="info-item"><strong>Email:</strong> <a href="mailto:${data.email}">${data.email}</a></div>` : ''}
                    ${data.phone ? `<div class="info-item"><strong>Phone:</strong> <a href="tel:${data.phone}">${data.phone}</a></div>` : ''}
                    <div class="info-item"><strong>Services:</strong> ${data.services ? data.services.length : 0}</div>
                    <div class="info-item"><strong>Joined:</strong> ${new Date(data.created_at).toLocaleDateString()}</div>
                </div>
                ${data.services && data.services.length > 0 ? `
                    <div class="services-section">
                        <strong>Assigned Services:</strong>
                        <div class="services-list">
                            ${data.services.map(service => `<span class="service-tag">${service.name}</span>`).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        </div>
    `;
}

function generateCategoryView(data) {
    return `
        <div class="entity-view category-view">
            <div class="view-header">
                <h3>${data.name}</h3>
                <div class="view-badges">
                    <span class="badge ${data.is_active ? 'badge-success' : 'badge-secondary'}">
                        ${data.is_active ? 'Active' : 'Inactive'}
                    </span>
                    <div class="color-indicator" style="background-color: ${data.color}; width: 20px; height: 20px; border-radius: 4px; display: inline-block;"></div>
                </div>
            </div>
            <div class="view-content">
                <div class="info-grid">
                    ${data.name_en ? `<div class="info-item"><strong>English Name:</strong> ${data.name_en}</div>` : ''}
                    ${data.icon ? `<div class="info-item"><strong>Icon:</strong> <i class="${data.icon}"></i> ${data.icon}</div>` : ''}
                    <div class="info-item"><strong>Sort Order:</strong> ${data.sort_order || 0}</div>
                    <div class="info-item"><strong>Services:</strong> ${data.services ? data.services.length : 0}</div>
                    <div class="info-item"><strong>Created:</strong> ${new Date(data.created_at).toLocaleDateString()}</div>
                </div>
                ${data.description ? `<div class="description-section"><strong>Description:</strong><p>${data.description}</p></div>` : ''}
                ${data.services && data.services.length > 0 ? `
                    <div class="services-section">
                        <strong>Services in this Category:</strong>
                        <div class="services-list">
                            ${data.services.map(service => `
                                <div class="service-item">
                                    <span class="service-name">${service.name}</span>
                                    <span class="service-price">€${parseFloat(service.price).toFixed(2)}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        </div>
    `;
}

function generateReservationView(data) {
    return `
        <div class="entity-view reservation-view">
            <div class="view-header">
                <h3>${data.customer_name}</h3>
                <div class="view-badges">
                    <span class="badge badge-${data.status === 'confirmed' ? 'success' : data.status === 'pending' ? 'warning' : data.status === 'completed' ? 'info' : 'danger'}">
                        ${data.status.charAt(0).toUpperCase() + data.status.slice(1)}
                    </span>
                </div>
            </div>
            <div class="view-content">
                <div class="info-grid">
                    <div class="info-item"><strong>Service:</strong> ${data.service_name}</div>
                    <div class="info-item"><strong>Date:</strong> ${new Date(data.date).toLocaleDateString()}</div>
                    <div class="info-item"><strong>Time:</strong> ${data.start_time}${data.end_time ? ` - ${data.end_time}` : ''}</div>
                    ${data.employee_name ? `<div class="info-item"><strong>Employee:</strong> ${data.employee_name}</div>` : ''}
                    <div class="info-item"><strong>Price:</strong> €${parseFloat(data.price).toFixed(2)}</div>
                    <div class="info-item"><strong>Duration:</strong> ${data.service_duration} minutes</div>
                    ${data.customer_phone ? `<div class="info-item"><strong>Phone:</strong> <a href="tel:${data.customer_phone}">${data.customer_phone}</a></div>` : ''}
                    <div class="info-item"><strong>Created:</strong> ${new Date(data.created_at).toLocaleDateString()}</div>
                </div>
                ${data.notes ? `<div class="notes-section"><strong>Notes:</strong><p>${data.notes}</p></div>` : ''}
            </div>
        </div>
    `;
}

// Bulk Operation Form Generators
function generateBulkEmailForm(selectedIds) {
    return `
        <form id="bulk-email-form" class="bulk-form">
            <div class="form-group">
                <label for="email-subject">Subject *</label>
                <input type="text" id="email-subject" name="subject" required>
            </div>
            <div class="form-group">
                <label for="email-message">Message *</label>
                <textarea id="email-message" name="message" rows="5" required></textarea>
            </div>
            <div class="selected-items">
                <p>Sending to ${selectedIds.length} customers</p>
            </div>
        </form>
    `;
}

function generateBulkSMSForm(selectedIds) {
    return `
        <form id="bulk-sms-form" class="bulk-form">
            <div class="form-group">
                <label for="sms-message">Message *</label>
                <textarea id="sms-message" name="message" rows="3" maxlength="160" required></textarea>
                <small class="form-text">Maximum 160 characters</small>
            </div>
            <div class="selected-items">
                <p>Sending to ${selectedIds.length} customers</p>
            </div>
        </form>
    `;
}

function generateBulkPriceForm(selectedIds) {
    return `
        <form id="bulk-price-form" class="bulk-form">
            <div class="form-group">
                <label for="price-action">Action</label>
                <select id="price-action" name="action" required>
                    <option value="increase">Increase by</option>
                    <option value="decrease">Decrease by</option>
                    <option value="set">Set to</option>
                </select>
            </div>
            <div class="form-group">
                <label for="price-value">Value (€)</label>
                <input type="number" id="price-value" name="value" step="0.01" min="0" required>
            </div>
            <div class="selected-items">
                <p>Updating ${selectedIds.length} services</p>
            </div>
        </form>
    `;
}

function generateBulkScheduleForm(selectedIds) {
    return `
        <form id="bulk-schedule-form" class="bulk-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="schedule-date">Date</label>
                    <input type="date" id="schedule-date" name="date" required>
                </div>
                <div class="form-group">
                    <label for="schedule-shift">Shift</label>
                    <select id="schedule-shift" name="shift" required>
                        <option value="morning">Morning (9:00-14:00)</option>
                        <option value="afternoon">Afternoon (14:00-19:00)</option>
                        <option value="full">Full Day (9:00-19:00)</option>
                    </select>
                </div>
            </div>
            <div class="selected-items">
                <p>Scheduling ${selectedIds.length} employees</p>
            </div>
        </form>
    `;
}

function generateBulkNotifyForm(selectedIds) {
    return `
        <form id="bulk-notify-form" class="bulk-form">
            <div class="form-group">
                <label for="notify-type">Notification Type</label>
                <select id="notify-type" name="type" required>
                    <option value="schedule">Schedule Update</option>
                    <option value="announcement">Announcement</option>
                    <option value="reminder">Reminder</option>
                </select>
            </div>
            <div class="form-group">
                <label for="notify-message">Message *</label>
                <textarea id="notify-message" name="message" rows="4" required></textarea>
            </div>
            <div class="selected-items">
                <p>Notifying ${selectedIds.length} employees</p>
            </div>
        </form>
    `;
}

function generateBulkReminderForm(selectedIds) {
    return `
        <form id="bulk-reminder-form" class="bulk-form">
            <div class="form-group">
                <label for="reminder-type">Reminder Type</label>
                <select id="reminder-type" name="type" required>
                    <option value="sms">SMS</option>
                    <option value="email">Email</option>
                    <option value="both">Both SMS & Email</option>
                </select>
            </div>
            <div class="form-group">
                <label for="reminder-time">Send Time</label>
                <select id="reminder-time" name="send_time" required>
                    <option value="now">Send Now</option>
                    <option value="1hour">1 Hour Before</option>
                    <option value="2hours">2 Hours Before</option>
                    <option value="1day">1 Day Before</option>
                </select>
            </div>
            <div class="selected-items">
                <p>Sending reminders for ${selectedIds.length} reservations</p>
            </div>
        </form>
    `;
}

function generateBulkRescheduleForm(selectedIds) {
    return `
        <form id="bulk-reschedule-form" class="bulk-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="reschedule-date">New Date</label>
                    <input type="date" id="reschedule-date" name="new_date" required>
                </div>
                <div class="form-group">
                    <label for="reschedule-time">New Time</label>
                    <input type="time" id="reschedule-time" name="new_time" required>
                </div>
            </div>
            <div class="form-group">
                <label for="reschedule-reason">Reason</label>
                <textarea id="reschedule-reason" name="reason" rows="2" placeholder="Optional reason for rescheduling"></textarea>
            </div>
            <div class="selected-items">
                <p>Rescheduling ${selectedIds.length} reservations</p>
            </div>
        </form>
    `;
}

function generateReorderForm() {
    return `
        <form id="reorder-form" class="reorder-form">
            <div class="form-group">
                <label>Drag and drop to reorder categories:</label>
                <div id="sortable-categories" class="sortable-list">
                    <p>Loading categories...</p>
                </div>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" name="update_services" checked>
                    Also update service sort order within categories
                </label>
            </div>
        </form>
    `;
}

function generateBulkReorderForm(selectedIds) {
    return `
        <form id="bulk-reorder-form" class="bulk-form">
            <div class="form-group">
                <label for="reorder-method">Reorder Method</label>
                <select id="reorder-method" name="method" required>
                    <option value="alphabetical">Alphabetical (A-Z)</option>
                    <option value="reverse-alphabetical">Reverse Alphabetical (Z-A)</option>
                    <option value="service-count">By Service Count</option>
                    <option value="creation-date">By Creation Date</option>
                    <option value="custom">Custom Order</option>
                </select>
            </div>
            <div class="form-group" id="custom-order-group" style="display: none;">
                <label>Custom Order (drag to reorder):</label>
                <div id="custom-sortable" class="sortable-list">
                    <!-- Will be populated dynamically -->
                </div>
            </div>
            <div class="selected-items">
                <p>Reordering ${selectedIds.length} categories</p>
            </div>
        </form>
    `;
}

function generateBulkColorForm(selectedIds) {
    return `
        <form id="bulk-color-form" class="bulk-form">
            <div class="form-group">
                <label for="color-method">Color Method</label>
                <select id="color-method" name="method" required>
                    <option value="single">Apply Single Color</option>
                    <option value="gradient">Apply Color Gradient</option>
                    <option value="random">Random Colors</option>
                    <option value="category-based">Category-Based Colors</option>
                </select>
            </div>
            <div class="form-group" id="single-color-group">
                <label for="single-color">Color</label>
                <input type="color" id="single-color" name="color" value="#3498db">
            </div>
            <div class="form-group" id="gradient-group" style="display: none;">
                <div class="form-row">
                    <div class="form-group">
                        <label for="start-color">Start Color</label>
                        <input type="color" id="start-color" name="start_color" value="#3498db">
                    </div>
                    <div class="form-group">
                        <label for="end-color">End Color</label>
                        <input type="color" id="end-color" name="end_color" value="#e74c3c">
                    </div>
                </div>
            </div>
            <div class="selected-items">
                <p>Updating colors for ${selectedIds.length} categories</p>
            </div>
        </form>
    `;
}

// Utility Functions
function showModal(title, content, onSubmit = null, showSaveButton = true) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('admin-modal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'admin-modal';
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title"></h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body"></div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary modal-cancel">Cancel</button>
                    <button type="button" class="btn btn-primary modal-save">Save</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Set content
    modal.querySelector('.modal-title').textContent = title;
    modal.querySelector('.modal-body').innerHTML = content;

    // Store submit handler
    modal.onSubmitHandler = onSubmit;

    // Show/hide save button based on parameter
    const saveButton = modal.querySelector('.modal-save');
    const cancelButton = modal.querySelector('.modal-cancel');

    if (showSaveButton && onSubmit) {
        saveButton.style.display = 'inline-block';
        saveButton.textContent = 'Save';
    } else if (!showSaveButton) {
        saveButton.style.display = 'none';
        cancelButton.textContent = 'Close';
    } else {
        saveButton.style.display = 'inline-block';
        saveButton.textContent = 'OK';
    }

    // Show modal
    modal.style.display = 'flex';

    // Set up event handlers
    const closeButton = modal.querySelector('.modal-close');

    // Remove old event listeners
    closeButton.replaceWith(closeButton.cloneNode(true));
    cancelButton.replaceWith(cancelButton.cloneNode(true));
    saveButton.replaceWith(saveButton.cloneNode(true));

    // Add new event listeners
    modal.querySelector('.modal-close').addEventListener('click', closeModal);
    modal.querySelector('.modal-cancel').addEventListener('click', closeModal);
    modal.querySelector('.modal-save').addEventListener('click', submitModal);

    // Close on backdrop click
    modal.onclick = function(e) {
        if (e.target === modal) closeModal();
    };

    // Focus first input
    setTimeout(() => {
        const firstInput = modal.querySelector('input, select, textarea');
        if (firstInput) {
            firstInput.focus();
        }
    }, 100);
}

function closeModal() {
    const modal = document.getElementById('admin-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function submitModal() {
    const modal = document.getElementById('admin-modal');
    if (modal && modal.onSubmitHandler) {
        const form = modal.querySelector('form');
        if (form) {
            const formData = new FormData(form);
            modal.onSubmitHandler(formData);
        }
    }
    closeModal();
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);

    // Close button handler
    notification.querySelector('.notification-close').onclick = function() {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    };
}

function submitForm(entity, action, formData, id = null) {
    const url = `/store-admin/controllers/ajax.php?action=${action}&entity=${entity}${id ? `&id=${id}` : ''}`;

    fetch(url, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`${entity.slice(0, -1)} ${action}ed successfully`, 'success');
            location.reload();
        } else {
            showNotification(data.message || `Failed to ${action} ${entity.slice(0, -1)}`, 'error');
        }
    })
    .catch(error => {
        showNotification(`Error: ${error.message}`, 'error');
    });
}

function submitBulkAction(action, selectedIds, formData = null) {
    const url = `/store-admin/controllers/ajax.php?action=bulk_${action}`;

    const data = new FormData();
    data.append('ids', JSON.stringify(selectedIds));

    if (formData) {
        for (let [key, value] of formData.entries()) {
            data.append(key, value);
        }
    }

    fetch(url, {
        method: 'POST',
        body: data
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Bulk ${action} completed successfully`, 'success');
            location.reload();
        } else {
            showNotification(data.message || `Failed to perform bulk ${action}`, 'error');
        }
    })
    .catch(error => {
        showNotification(`Error: ${error.message}`, 'error');
    });
}

// Global functions that need to be available immediately
function changePerPage(value) {
    currentPerPage = value;
    performSearch();
}

function clearSearch() {
    const searchInput = document.querySelector('#search-input');
    if (searchInput) {
        searchInput.value = '';
        performSearch();
    }
}

function handleSearch(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        performSearch();
    }
}

function performSearch() {
    try {
        const searchInput = document.querySelector('#search-input');
        const currentUrl = new URL(window.location);

        if (searchInput) {
            currentSearch = searchInput.value.trim();

            if (currentSearch) {
                currentUrl.searchParams.set('search', currentSearch);
            } else {
                currentUrl.searchParams.delete('search');
            }
        }

        currentUrl.searchParams.set('per_page', currentPerPage);
        currentUrl.searchParams.set('page_num', 1); // Reset to first page on new search

        // Add a small loading indicator
        const searchButton = document.querySelector('.search-filter-bar button');
        if (searchButton) {
            searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';
            searchButton.disabled = true;
        }

        window.location.href = currentUrl.toString();
    } catch (error) {
        console.error('Search error:', error);
        // Reset button if error occurs
        const searchButton = document.querySelector('.search-filter-bar button');
        if (searchButton) {
            searchButton.innerHTML = '<i class="fas fa-times"></i> Clear';
            searchButton.disabled = false;
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeAdmin();
});

/**
 * Initialize admin interface
 */
function initializeAdmin() {
    // Initialize current values from URL
    initializeCurrentValues();

    // Setup search functionality
    setupSearch();

    // Setup pagination
    setupPagination();

    // Setup form validation
    setupFormValidation();

    // Setup modal functionality
    setupModal();

    // Setup auto-refresh for real-time updates
    setupAutoRefresh();

    // Setup services accordion
    setupServicesAccordion();
}

/**
 * Setup accordion functionality
 */
function setupServicesAccordion() {
    // Setup all accordion headers
    const accordionHeaders = document.querySelectorAll('.accordion-header');

    accordionHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const content = document.getElementById(targetId);
            const arrow = this.querySelector('.accordion-arrow');

            if (content && arrow) {
                content.classList.toggle('collapsed');
                arrow.classList.toggle('rotated');
            }
        });
    });
}

/**
 * Initialize current values from URL parameters
 */
function initializeCurrentValues() {
    const urlParams = new URLSearchParams(window.location.search);

    // Update current values from URL
    currentSearch = urlParams.get('search') || '';
    currentPage = parseInt(urlParams.get('page_num')) || 1;
    currentPerPage = parseInt(urlParams.get('per_page')) || 20;
}

/**
 * Setup search functionality
 */
function setupSearch() {
    const searchInput = document.querySelector('#search-input');
    const searchButton = document.querySelector('#search-button');
    const perPageSelect = document.querySelector('#per-page-select');

    if (searchInput) {
        // Set initial value from URL
        searchInput.value = currentSearch;

        // Handle Enter key only (no auto-search)
        searchInput.addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                performSearch();
            }
        });
    }

    if (searchButton) {
        // Handle search button click
        searchButton.addEventListener('click', function(event) {
            event.preventDefault();
            performSearch();
        });
    }

    if (perPageSelect) {
        // Set initial value from URL
        perPageSelect.value = currentPerPage;

        perPageSelect.addEventListener('change', function() {
            currentPerPage = this.value;
            performSearch();
        });
    }
}



/**
 * Setup pagination
 */
function setupPagination() {
    const paginationLinks = document.querySelectorAll('.pagination a[href]');

    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Extract page number from href
            const url = new URL(this.href, window.location.origin);
            const page = url.searchParams.get('page_num');

            if (page) {
                navigateToPage(page);
            }
        });
    });
}

/**
 * Navigate to specific page
 */
function navigateToPage(page) {
    try {
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.set('page_num', page);

        // Preserve all existing parameters (search, filters, etc.)
        window.location.href = currentUrl.toString();
    } catch (error) {
        console.error('Navigation error:', error);
        // Fallback: reload page with page parameter
        window.location.href = window.location.pathname + '?page_num=' + page;
    }
}

/**
 * Setup form validation
 */
function setupFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
    });
}

/**
 * Validate form fields
 */
function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
        } else {
            clearFieldError(field);
        }
    });
    
    // Email validation
    const emailFields = form.querySelectorAll('input[type="email"]');
    emailFields.forEach(field => {
        if (field.value && !isValidEmail(field.value)) {
            showFieldError(field, 'Please enter a valid email address');
            isValid = false;
        }
    });
    
    // Phone validation
    const phoneFields = form.querySelectorAll('input[type="tel"]');
    phoneFields.forEach(field => {
        if (field.value && !isValidPhone(field.value)) {
            showFieldError(field, 'Please enter a valid phone number');
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * Show field error
 */
function showFieldError(field, message) {
    clearFieldError(field);
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    errorDiv.style.color = 'var(--danger-color)';
    errorDiv.style.fontSize = '0.875rem';
    errorDiv.style.marginTop = '0.25rem';
    
    field.parentNode.appendChild(errorDiv);
    field.style.borderColor = 'var(--danger-color)';
}

/**
 * Clear field error
 */
function clearFieldError(field) {
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    field.style.borderColor = '';
}

/**
 * Email validation
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Phone validation (Greek format)
 */
function isValidPhone(phone) {
    const phoneRegex = /^(69|21|22|23|24|25|26|27|28)\d{8}$/;
    const cleanPhone = phone.replace(/\D/g, '');
    return phoneRegex.test(cleanPhone);
}

/**
 * Show modal
 */
function showModal(title, content) {
    const modal = document.getElementById('modal-overlay');
    const modalTitle = document.getElementById('modal-title');
    const modalBody = document.getElementById('modal-body');

    if (modal && modalTitle && modalBody) {
        modalTitle.textContent = title;
        modalBody.innerHTML = content;
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
}

/**
 * Close modal
 */
function closeModal() {
    const modal = document.getElementById('modal-overlay');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }
}

/**
 * Setup modal functionality
 */
function setupModal() {
    const modal = document.getElementById('modal-overlay');
    const closeBtn = document.querySelector('.modal-close');
    
    if (closeBtn) {
        closeBtn.addEventListener('click', closeModal);
    }
    
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });
    }
    
    // ESC key to close modal
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal && modal.style.display !== 'none') {
            closeModal();
        }
    });
}

/**
 * Show modal
 */
function showModal(title, content) {
    const modal = document.getElementById('modal-overlay');
    const modalTitle = document.getElementById('modal-title');
    const modalBody = document.getElementById('modal-body');
    
    if (modal && modalTitle && modalBody) {
        modalTitle.textContent = title;
        modalBody.innerHTML = content;
        modal.style.display = 'flex';
        
        // Focus first input in modal
        const firstInput = modalBody.querySelector('input, select, textarea');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
    }
}

/**
 * Close modal
 */
function closeModal() {
    const modal = document.getElementById('modal-overlay');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * Setup auto-refresh for real-time updates
 */
function setupAutoRefresh() {
    // Auto-refresh every 5 minutes for dashboard
    if (window.location.search.includes('page=dashboard') || 
        window.location.pathname.endsWith('/store-admin/')) {
        setInterval(() => {
            refreshDashboard();
        }, 300000); // 5 minutes
    }
}

/**
 * Refresh dashboard statistics
 */
function refreshDashboard() {
    fetch('/store-admin/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'ajax=1&action=get_dashboard_stats'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateDashboardStats(data.stats);
        }
    })
    .catch(error => console.error('Error refreshing dashboard:', error));
}

/**
 * Update dashboard statistics
 */
function updateDashboardStats(stats) {
    // Update stat numbers
    Object.keys(stats).forEach(key => {
        const element = document.querySelector(`[data-stat="${key}"]`);
        if (element) {
            element.textContent = stats[key];
        }
    });
}

/**
 * Add new item
 */
function addItem(type) {
    currentEditId = null;
    loadItemForm(type, null);
}

/**
 * Edit item
 */
function editItem(type, id) {
    currentEditId = id;
    loadItemForm(type, id);
}

/**
 * Get current page type from URL
 */
function getCurrentPageType() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('page') || 'dashboard';
}



/**
 * Load item form
 */
function loadItemForm(type, id) {
    const action = id ? 'edit' : 'add';
    const title = `${action === 'add' ? 'Add' : 'Edit'} ${type.charAt(0).toUpperCase() + type.slice(0, -1)}`;

    // Add cache busting timestamp
    const timestamp = Date.now();

    fetch('/store-admin/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        },
        body: `ajax=1&action=get_form&type=${type}&id=${id || ''}&_t=${timestamp}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showModal(title, data.form);
            setupModalForm();
        } else {
            alert('Error loading form: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error loading form:', error);
        alert('Error loading form. Please try again.');
    });
}

/**
 * Setup modal form
 */
function setupModalForm() {
    const form = document.querySelector('#modal-body form');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            submitModalForm(this);
        });
    }
}

/**
 * Submit modal form
 */
function submitModalForm(form) {
    const formData = new FormData(form);
    formData.append('ajax', '1');

    // Determine the correct action based on current page
    const currentPage = new URLSearchParams(window.location.search).get('page') || 'dashboard';
    const action = formData.get('action');

    if (action === 'save') {
        // Convert generic 'save' action to specific AJAX action
        const actionMap = {
            'categories': 'save_category',
            'services': 'save_service',
            'employees': 'save_employee',
            'customers': 'save_customer',
            'reservations': 'save_reservation'
        };

        if (actionMap[currentPage]) {
            formData.set('action', actionMap[currentPage]);
        }
    }

    fetch('/store-admin/', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal();
            showSuccess(data.message || 'Item saved successfully');
            setTimeout(() => {
                // Force a hard refresh to avoid caching issues
                location.reload(true);
            }, 1000);
        } else {
            showError(data.error || 'Error saving item');
        }
    })
    .catch(error => {
        console.error('Error saving item:', error);
        showError('Error saving item. Please try again.');
    });
}

/**
 * Delete item
 */
function deleteItem(table, id) {
    if (!confirm('Are you sure you want to delete this item?')) {
        return;
    }
    
    fetch('/store-admin/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `ajax=1&action=delete_item&table=${table}&id=${id}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('Item deleted successfully');
            setTimeout(() => {
                // Force a hard refresh to avoid caching issues
                location.reload(true);
            }, 1000);
        } else {
            showError(data.error || 'Error deleting item');
        }
    })
    .catch(error => {
        console.error('Error deleting item:', error);
        showError('Error deleting item. Please try again.');
    });
}

/**
 * Get current page type
 */
function getCurrentPageType() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('page') || 'dashboard';
}

/**
 * Show success message
 */
function showSuccess(message) {
    showNotification(message, 'success');
}

/**
 * Show error message
 */
function showError(message) {
    showNotification(message, 'error');
}

/**
 * Show notification
 */
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add notification styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? 'var(--success-color)' : 'var(--danger-color)'};
        color: white;
        padding: 15px 20px;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 3000;
        display: flex;
        align-items: center;
        gap: 10px;
        max-width: 400px;
        animation: slideIn 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Format currency
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('el-GR', {
        style: 'currency',
        currency: 'EUR'
    }).format(amount);
}

/**
 * Format date
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('el-GR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

/**
 * Format time
 */
function formatTime(timeString) {
    const [hours, minutes] = timeString.split(':');
    return `${hours}:${minutes}`;
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Throttle function
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Get services by category (for reservations)
 */
function getServicesByCategory(categoryId) {
    const serviceSelect = document.getElementById('service_id');
    
    if (!serviceSelect) return;
    
    fetch('/store-admin/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `ajax=1&action=get_services&category_id=${categoryId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            serviceSelect.innerHTML = '<option value="">Select Service</option>';
            data.services.forEach(service => {
                const option = document.createElement('option');
                option.value = service.id;
                option.textContent = `${service.name} (${formatCurrency(service.price)})`;
                serviceSelect.appendChild(option);
            });
        }
    })
    .catch(error => console.error('Error loading services:', error));
}

/**
 * Get available time slots
 */
function getAvailableSlots(date, serviceId, employeeId = '') {
    const slotsContainer = document.getElementById('available-slots');
    
    if (!slotsContainer) return;
    
    slotsContainer.innerHTML = '<div class="loading">Loading available slots...</div>';
    
    fetch('/store-admin/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `ajax=1&action=get_available_slots&date=${date}&service_id=${serviceId}&employee_id=${employeeId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.slots.length === 0) {
                slotsContainer.innerHTML = '<div class="no-slots">No available slots for this date</div>';
            } else {
                slotsContainer.innerHTML = data.slots.map(slot => 
                    `<button type="button" class="slot-btn" onclick="selectSlot('${slot}')">${slot}</button>`
                ).join('');
            }
        } else {
            slotsContainer.innerHTML = '<div class="error">Error loading slots</div>';
        }
    })
    .catch(error => {
        console.error('Error loading slots:', error);
        slotsContainer.innerHTML = '<div class="error">Error loading slots</div>';
    });
}

/**
 * Select time slot
 */
function selectSlot(time) {
    const timeInput = document.getElementById('start_time');
    const slotButtons = document.querySelectorAll('.slot-btn');
    
    if (timeInput) {
        timeInput.value = time;
    }
    
    slotButtons.forEach(btn => btn.classList.remove('selected'));
    event.target.classList.add('selected');
}

/**
 * Copy text to clipboard
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showSuccess('Copied to clipboard');
    }).catch(err => {
        console.error('Failed to copy:', err);
        showError('Failed to copy to clipboard');
    });
}

/**
 * Export data
 */
function exportData(type, format = 'csv') {
    const params = new URLSearchParams(window.location.search);
    params.set('action', 'export');
    params.set('format', format);
    
    const url = `/store-admin/?${params.toString()}`;
    window.open(url, '_blank');
}

/**
 * Print current page
 */
function printPage() {
    window.print();
}

/**
 * Toggle day hours visibility for employee working hours
 */
function toggleDayHours(day) {
    const checkbox = document.querySelector(`input[name="working_days[]"][value="${day}"]`);
    const timesDiv = document.getElementById(`${day}_times`);

    if (checkbox && timesDiv) {
        if (checkbox.checked) {
            timesDiv.style.display = 'flex';
        } else {
            timesDiv.style.display = 'none';
        }
    }
}

/**
 * Make toggleDayHours globally available
 */
window.toggleDayHours = toggleDayHours;

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .notification {
        animation: slideIn 0.3s ease-out;
    }
    
    .notification-close {
        background: none;
        border: none;
        color: inherit;
        cursor: pointer;
        font-size: 16px;
        padding: 0;
        margin-left: 10px;
    }
    
    .loading {
        text-align: center;
        padding: 20px;
        color: var(--text-muted);
    }
    
    .no-slots, .error {
        text-align: center;
        padding: 20px;
        color: var(--text-muted);
    }
    
    .error {
        color: var(--danger-color);
    }
    
    .slot-btn {
        margin: 5px;
        padding: 8px 15px;
        border: 1px solid var(--border-color);
        background: white;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
    }
    
    .slot-btn:hover {
        background: var(--light-color);
        border-color: var(--primary-color);
    }
    
    .slot-btn.selected {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }
`;
document.head.appendChild(style);

// Special Days functionality
function initializeSpecialDays() {
    // Handle schedule type radio buttons
    const scheduleTypeRadios = document.querySelectorAll('input[name="schedule_type"]');
    const customHoursDiv = document.getElementById('custom-hours');

    if (scheduleTypeRadios.length > 0 && customHoursDiv) {
        scheduleTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'general_hours') {
                    customHoursDiv.style.display = 'block';
                } else {
                    customHoursDiv.style.display = 'none';
                }
            });
        });

        // Initialize based on current selection
        const selectedRadio = document.querySelector('input[name="schedule_type"]:checked');
        if (selectedRadio && selectedRadio.value === 'general_hours') {
            customHoursDiv.style.display = 'block';
        }
    }
}

// Function to show special day form
function showSpecialDayForm() {
    const formHtml = `
        <form method="POST" action="">
            <input type="hidden" name="action" value="save_special_day">

            <div class="form-group">
                <label for="special_date">Date</label>
                <input type="date" id="special_date" name="special_date" required>
            </div>

            <div class="form-group">
                <label for="special_note">Note (optional)</label>
                <input type="text" id="special_note" name="special_note" placeholder="Holiday, Special Event, etc.">
            </div>

            <div class="form-group">
                <label>Schedule Type</label>
                <div class="radio-group">
                    <label><input type="radio" name="schedule_type" value="closed" checked> Closed All Day</label>
                    <label><input type="radio" name="schedule_type" value="general_hours"> Custom Hours</label>
                </div>
            </div>

            <div id="custom-hours" style="display: none;">
                <div class="form-group">
                    <label>Hours</label>
                    <div class="time-period">
                        <input type="time" name="general_start[]" value="09:00">
                        <span>to</span>
                        <input type="time" name="general_end[]" value="17:00">
                    </div>
                </div>
            </div>

            <button type="submit" class="btn btn-primary">Save Special Day</button>
        </form>
    `;

    // Create modal or replace content
    const container = document.querySelector('.special-days-form-container');
    if (container) {
        container.innerHTML = formHtml;
        // Re-initialize the form functionality
        initializeSpecialDays();
    }
}

// Initialize special days when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeSpecialDays();
});
