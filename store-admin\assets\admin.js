/**
 * Enhanced Store Admin JavaScript
 * Handles all client-side functionality for the modern admin interface
 */

// Global variables
let currentEditId = null;
let currentPage = 1;
let currentSearch = '';
let currentPerPage = 20;

// Enhanced UI Initialization
document.addEventListener('DOMContentLoaded', function() {
    initializeEnhancedUI();
    initializeBulkActions();
    initializeViewToggle();
    initializeEnhancedSearch();
    initializeFilters();
    initializeSidebar();
    initializeTooltips();
    initializeModals();
    initializeFormValidation();
});

// Enhanced UI Components
function initializeEnhancedUI() {
    // Initialize entity selection
    const entitySelects = document.querySelectorAll('.entity-select');
    entitySelects.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    // Initialize card interactions
    const entityCards = document.querySelectorAll('.entity-card:not(.add-card)');
    entityCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            if (!this.classList.contains('selected')) {
                this.style.transform = 'translateY(-2px)';
            }
        });

        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('selected')) {
                this.style.transform = 'translateY(0)';
            }
        });
    });
}

// Bulk Actions Management
function initializeBulkActions() {
    window.updateBulkActions = function() {
        const selected = document.querySelectorAll('.entity-select:checked');
        const count = selected.length;
        const bulkActions = document.getElementById('bulk-actions');
        const selectedCount = document.getElementById('selected-count');

        if (selectedCount) {
            selectedCount.textContent = count;
        }

        if (bulkActions) {
            if (count > 0) {
                bulkActions.classList.add('show');
            } else {
                bulkActions.classList.remove('show');
            }
        }

        // Update visual state
        document.querySelectorAll('.entity-card').forEach(card => {
            const checkbox = card.querySelector('.entity-select');
            if (checkbox && checkbox.checked) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }
        });
    };

    // Select all functionality
    window.toggleSelectAll = function() {
        const checkboxes = document.querySelectorAll('.entity-select');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });

        updateBulkActions();
    };
}

// View Toggle
function initializeViewToggle() {
    const viewToggleButtons = document.querySelectorAll('.view-toggle button');

    viewToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            viewToggleButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            const viewType = this.dataset.view;
            toggleView(viewType);
        });
    });
}

function toggleView(viewType) {
    const grid = document.querySelector('.entity-grid');
    if (grid) {
        if (viewType === 'table') {
            grid.style.display = 'none';
            showTableView();
        } else {
            grid.style.display = 'grid';
            hideTableView();
        }
    }
}

// Enhanced Search
function initializeEnhancedSearch() {
    const searchInputs = document.querySelectorAll('.search-bar input, .header-search input');

    searchInputs.forEach(input => {
        let searchTimeout;

        input.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performEnhancedSearch(this.value);
            }, 300);
        });

        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                performEnhancedSearch(this.value);
            }
        });
    });
}

function performEnhancedSearch(query) {
    const urlParams = new URLSearchParams(window.location.search);

    if (query.trim()) {
        urlParams.set('search', query);
    } else {
        urlParams.delete('search');
    }

    urlParams.set('page', '1'); // Reset to first page
    window.location.href = window.location.pathname + '?' + urlParams.toString();
}

// Filter Management
function initializeFilters() {
    const filters = document.querySelectorAll('select[id$="-filter"]');

    filters.forEach(filter => {
        filter.addEventListener('change', function() {
            applyFilters();
        });
    });
}

function applyFilters() {
    const urlParams = new URLSearchParams(window.location.search);
    const filters = document.querySelectorAll('select[id$="-filter"]');

    filters.forEach(filter => {
        const paramName = filter.id.replace('-filter', '');
        if (filter.value) {
            urlParams.set(paramName, filter.value);
        } else {
            urlParams.delete(paramName);
        }
    });

    urlParams.set('page', '1'); // Reset to first page
    window.location.href = window.location.pathname + '?' + urlParams.toString();
}

// Enhanced Customer Functions
function addCustomer() {
    showModal('Add Customer', generateCustomerForm(), function(formData) {
        submitForm('customers', 'add', formData);
    });
}

function editCustomer(id) {
    // Fetch customer data and show edit form
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=customers&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Edit Customer', generateCustomerForm(data.data), function(formData) {
                    submitForm('customers', 'edit', formData, id);
                });
            }
        });
}

function viewCustomer(id) {
    // Show customer details in modal
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=customers&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Customer Details', generateCustomerView(data.data));
            }
        });
}

function bookAppointment(customerId) {
    // Redirect to reservations page with customer pre-selected
    window.location.href = `/store-admin/?page=reservations&action=add&customer_id=${customerId}`;
}

function bulkEmail() {
    const selected = getSelectedCustomers();
    if (selected.length === 0) {
        showNotification('Please select customers first', 'warning');
        return;
    }

    showModal('Send Email', generateBulkEmailForm(selected), function(formData) {
        // Handle bulk email
        submitBulkAction('email', selected, formData);
    });
}

function bulkSMS() {
    const selected = getSelectedCustomers();
    if (selected.length === 0) {
        showNotification('Please select customers first', 'warning');
        return;
    }

    showModal('Send SMS', generateBulkSMSForm(selected), function(formData) {
        // Handle bulk SMS
        submitBulkAction('sms', selected, formData);
    });
}

function bulkExport() {
    const selected = getSelectedCustomers();
    if (selected.length === 0) {
        showNotification('Please select customers first', 'warning');
        return;
    }

    // Export selected customers
    const ids = selected.join(',');
    window.open(`/store-admin/controllers/ajax.php?action=export&entity=customers&ids=${ids}`, '_blank');
}

function bulkDelete() {
    const selected = getSelectedCustomers();
    if (selected.length === 0) {
        showNotification('Please select customers first', 'warning');
        return;
    }

    if (confirm(`Are you sure you want to delete ${selected.length} customers? This action cannot be undone.`)) {
        submitBulkAction('delete', selected);
    }
}

function getSelectedCustomers() {
    const checkboxes = document.querySelectorAll('.entity-select:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// Sidebar Management
function initializeSidebar() {
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    const overlay = document.querySelector('.sidebar-overlay');

    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('open');
            if (overlay) {
                overlay.classList.toggle('show');
            }
        });
    }

    if (overlay) {
        overlay.addEventListener('click', function() {
            sidebar.classList.remove('open');
            this.classList.remove('show');
        });
    }

    // Handle responsive behavior
    function handleResize() {
        if (window.innerWidth > 1024) {
            sidebar.classList.remove('open');
            if (overlay) {
                overlay.classList.remove('show');
            }
        }
    }

    window.addEventListener('resize', handleResize);
}

// Enhanced Service Functions
function addService() {
    showModal('Add Service', generateServiceForm(), function(formData) {
        submitForm('services', 'add', formData);
    });
}

function editService(id) {
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=services&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Edit Service', generateServiceForm(data.data), function(formData) {
                    submitForm('services', 'edit', formData, id);
                });
            }
        });
}

function viewService(id) {
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=services&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Service Details', generateServiceView(data.data));
            }
        });
}

function duplicateService(id) {
    if (confirm('Create a copy of this service?')) {
        fetch(`/store-admin/controllers/ajax.php?action=duplicate&entity=services&id=${id}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Service duplicated successfully', 'success');
                location.reload();
            } else {
                showNotification('Failed to duplicate service', 'error');
            }
        });
    }
}

function manageCategories() {
    showModal('Manage Categories', generateCategoriesManager(), function(formData) {
        // Handle category management
    });
}

function bulkUpdatePrices() {
    const selected = getSelectedServices();
    if (selected.length === 0) {
        showNotification('Please select services first', 'warning');
        return;
    }

    showModal('Update Prices', generateBulkPriceForm(selected), function(formData) {
        submitBulkAction('update_prices', selected, formData);
    });
}

function bulkDuplicate() {
    const selected = getSelectedServices();
    if (selected.length === 0) {
        showNotification('Please select services first', 'warning');
        return;
    }

    if (confirm(`Duplicate ${selected.length} services?`)) {
        submitBulkAction('duplicate', selected);
    }
}

function getSelectedServices() {
    const checkboxes = document.querySelectorAll('.entity-select:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// Enhanced Employee Functions
function addEmployee() {
    showModal('Add Employee', generateEmployeeForm(), function(formData) {
        submitForm('employees', 'add', formData);
    });
}

function editEmployee(id) {
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=employees&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Edit Employee', generateEmployeeForm(data.data), function(formData) {
                    submitForm('employees', 'edit', formData, id);
                });
            }
        });
}

function viewEmployee(id) {
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=employees&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Employee Details', generateEmployeeView(data.data));
            }
        });
}

function manageSchedule(employeeId) {
    window.location.href = `/store-admin/?page=schedules&employee_id=${employeeId}`;
}

function manageSchedules() {
    window.location.href = `/store-admin/?page=schedules`;
}

function bulkSchedule() {
    const selected = getSelectedEmployees();
    if (selected.length === 0) {
        showNotification('Please select employees first', 'warning');
        return;
    }

    showModal('Bulk Schedule', generateBulkScheduleForm(selected), function(formData) {
        submitBulkAction('schedule', selected, formData);
    });
}

function bulkNotify() {
    const selected = getSelectedEmployees();
    if (selected.length === 0) {
        showNotification('Please select employees first', 'warning');
        return;
    }

    showModal('Send Notification', generateBulkNotifyForm(selected), function(formData) {
        submitBulkAction('notify', selected, formData);
    });
}

function getSelectedEmployees() {
    const checkboxes = document.querySelectorAll('.entity-select:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// Enhanced Reservation Functions
function addReservation() {
    showModal('New Reservation', generateReservationForm(), function(formData) {
        submitForm('reservations', 'add', formData);
    });
}

function editReservation(id) {
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=reservations&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Edit Reservation', generateReservationForm(data.data), function(formData) {
                    submitForm('reservations', 'edit', formData, id);
                });
            }
        });
}

function viewReservation(id) {
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=reservations&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Reservation Details', generateReservationView(data.data));
            }
        });
}

function confirmReservation(id) {
    if (confirm('Confirm this reservation?')) {
        updateReservationStatus(id, 'confirmed');
    }
}

function completeReservation(id) {
    if (confirm('Mark this reservation as completed?')) {
        updateReservationStatus(id, 'completed');
    }
}

function updateReservationStatus(id, status) {
    fetch(`/store-admin/controllers/ajax.php?action=update_status&entity=reservations&id=${id}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: status })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Reservation ${status} successfully`, 'success');
            location.reload();
        } else {
            showNotification('Failed to update reservation', 'error');
        }
    });
}

function showCalendar() {
    window.location.href = `/store-admin/?page=calendar`;
}

function bulkConfirm() {
    const selected = getSelectedReservations();
    if (selected.length === 0) {
        showNotification('Please select reservations first', 'warning');
        return;
    }

    if (confirm(`Confirm ${selected.length} reservations?`)) {
        submitBulkAction('confirm', selected);
    }
}

function bulkRemind() {
    const selected = getSelectedReservations();
    if (selected.length === 0) {
        showNotification('Please select reservations first', 'warning');
        return;
    }

    showModal('Send Reminders', generateBulkReminderForm(selected), function(formData) {
        submitBulkAction('remind', selected, formData);
    });
}

function bulkReschedule() {
    const selected = getSelectedReservations();
    if (selected.length === 0) {
        showNotification('Please select reservations first', 'warning');
        return;
    }

    showModal('Bulk Reschedule', generateBulkRescheduleForm(selected), function(formData) {
        submitBulkAction('reschedule', selected, formData);
    });
}

function bulkCancel() {
    const selected = getSelectedReservations();
    if (selected.length === 0) {
        showNotification('Please select reservations first', 'warning');
        return;
    }

    if (confirm(`Cancel ${selected.length} reservations? This action cannot be undone.`)) {
        submitBulkAction('cancel', selected);
    }
}

function getSelectedReservations() {
    const checkboxes = document.querySelectorAll('.entity-select:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// Enhanced Category Functions
function addCategory() {
    showModal('Add Category', generateCategoryForm(), function(formData) {
        submitForm('categories', 'add', formData);
    });
}

function editCategory(id) {
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=categories&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Edit Category', generateCategoryForm(data.data), function(formData) {
                    submitForm('categories', 'edit', formData, id);
                });
            }
        });
}

function viewCategory(id) {
    fetch(`/store-admin/controllers/ajax.php?action=get&entity=categories&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showModal('Category Details', generateCategoryView(data.data));
            }
        });
}

function manageServices(categoryId) {
    window.location.href = `/store-admin/?page=services&category_id=${categoryId}`;
}

function reorderCategories() {
    showModal('Reorder Categories', generateReorderForm(), function(formData) {
        submitBulkAction('reorder', [], formData);
    });
}

function bulkReorder() {
    const selected = getSelectedCategories();
    if (selected.length === 0) {
        showNotification('Please select categories first', 'warning');
        return;
    }

    showModal('Bulk Reorder', generateBulkReorderForm(selected), function(formData) {
        submitBulkAction('reorder', selected, formData);
    });
}

function bulkUpdateColors() {
    const selected = getSelectedCategories();
    if (selected.length === 0) {
        showNotification('Please select categories first', 'warning');
        return;
    }

    showModal('Update Colors', generateBulkColorForm(selected), function(formData) {
        submitBulkAction('update_colors', selected, formData);
    });
}

function getSelectedCategories() {
    const checkboxes = document.querySelectorAll('.entity-select:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// Global functions that need to be available immediately
function changePerPage(value) {
    currentPerPage = value;
    performSearch();
}

function clearSearch() {
    const searchInput = document.querySelector('#search-input');
    if (searchInput) {
        searchInput.value = '';
        performSearch();
    }
}

function handleSearch(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        performSearch();
    }
}

function performSearch() {
    try {
        const searchInput = document.querySelector('#search-input');
        const currentUrl = new URL(window.location);

        if (searchInput) {
            currentSearch = searchInput.value.trim();

            if (currentSearch) {
                currentUrl.searchParams.set('search', currentSearch);
            } else {
                currentUrl.searchParams.delete('search');
            }
        }

        currentUrl.searchParams.set('per_page', currentPerPage);
        currentUrl.searchParams.set('page_num', 1); // Reset to first page on new search

        // Add a small loading indicator
        const searchButton = document.querySelector('.search-filter-bar button');
        if (searchButton) {
            searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';
            searchButton.disabled = true;
        }

        window.location.href = currentUrl.toString();
    } catch (error) {
        console.error('Search error:', error);
        // Reset button if error occurs
        const searchButton = document.querySelector('.search-filter-bar button');
        if (searchButton) {
            searchButton.innerHTML = '<i class="fas fa-times"></i> Clear';
            searchButton.disabled = false;
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeAdmin();
});

/**
 * Initialize admin interface
 */
function initializeAdmin() {
    // Initialize current values from URL
    initializeCurrentValues();

    // Setup search functionality
    setupSearch();

    // Setup pagination
    setupPagination();

    // Setup form validation
    setupFormValidation();

    // Setup modal functionality
    setupModal();

    // Setup auto-refresh for real-time updates
    setupAutoRefresh();

    // Setup services accordion
    setupServicesAccordion();
}

/**
 * Setup accordion functionality
 */
function setupServicesAccordion() {
    // Setup all accordion headers
    const accordionHeaders = document.querySelectorAll('.accordion-header');

    accordionHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const content = document.getElementById(targetId);
            const arrow = this.querySelector('.accordion-arrow');

            if (content && arrow) {
                content.classList.toggle('collapsed');
                arrow.classList.toggle('rotated');
            }
        });
    });
}

/**
 * Initialize current values from URL parameters
 */
function initializeCurrentValues() {
    const urlParams = new URLSearchParams(window.location.search);

    // Update current values from URL
    currentSearch = urlParams.get('search') || '';
    currentPage = parseInt(urlParams.get('page_num')) || 1;
    currentPerPage = parseInt(urlParams.get('per_page')) || 20;
}

/**
 * Setup search functionality
 */
function setupSearch() {
    const searchInput = document.querySelector('#search-input');
    const searchButton = document.querySelector('#search-button');
    const perPageSelect = document.querySelector('#per-page-select');

    if (searchInput) {
        // Set initial value from URL
        searchInput.value = currentSearch;

        // Handle Enter key only (no auto-search)
        searchInput.addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                performSearch();
            }
        });
    }

    if (searchButton) {
        // Handle search button click
        searchButton.addEventListener('click', function(event) {
            event.preventDefault();
            performSearch();
        });
    }

    if (perPageSelect) {
        // Set initial value from URL
        perPageSelect.value = currentPerPage;

        perPageSelect.addEventListener('change', function() {
            currentPerPage = this.value;
            performSearch();
        });
    }
}



/**
 * Setup pagination
 */
function setupPagination() {
    const paginationLinks = document.querySelectorAll('.pagination a[href]');

    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Extract page number from href
            const url = new URL(this.href, window.location.origin);
            const page = url.searchParams.get('page_num');

            if (page) {
                navigateToPage(page);
            }
        });
    });
}

/**
 * Navigate to specific page
 */
function navigateToPage(page) {
    try {
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.set('page_num', page);

        // Preserve all existing parameters (search, filters, etc.)
        window.location.href = currentUrl.toString();
    } catch (error) {
        console.error('Navigation error:', error);
        // Fallback: reload page with page parameter
        window.location.href = window.location.pathname + '?page_num=' + page;
    }
}

/**
 * Setup form validation
 */
function setupFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
    });
}

/**
 * Validate form fields
 */
function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
        } else {
            clearFieldError(field);
        }
    });
    
    // Email validation
    const emailFields = form.querySelectorAll('input[type="email"]');
    emailFields.forEach(field => {
        if (field.value && !isValidEmail(field.value)) {
            showFieldError(field, 'Please enter a valid email address');
            isValid = false;
        }
    });
    
    // Phone validation
    const phoneFields = form.querySelectorAll('input[type="tel"]');
    phoneFields.forEach(field => {
        if (field.value && !isValidPhone(field.value)) {
            showFieldError(field, 'Please enter a valid phone number');
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * Show field error
 */
function showFieldError(field, message) {
    clearFieldError(field);
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    errorDiv.style.color = 'var(--danger-color)';
    errorDiv.style.fontSize = '0.875rem';
    errorDiv.style.marginTop = '0.25rem';
    
    field.parentNode.appendChild(errorDiv);
    field.style.borderColor = 'var(--danger-color)';
}

/**
 * Clear field error
 */
function clearFieldError(field) {
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    field.style.borderColor = '';
}

/**
 * Email validation
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Phone validation (Greek format)
 */
function isValidPhone(phone) {
    const phoneRegex = /^(69|21|22|23|24|25|26|27|28)\d{8}$/;
    const cleanPhone = phone.replace(/\D/g, '');
    return phoneRegex.test(cleanPhone);
}

/**
 * Show modal
 */
function showModal(title, content) {
    const modal = document.getElementById('modal-overlay');
    const modalTitle = document.getElementById('modal-title');
    const modalBody = document.getElementById('modal-body');

    if (modal && modalTitle && modalBody) {
        modalTitle.textContent = title;
        modalBody.innerHTML = content;
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
}

/**
 * Close modal
 */
function closeModal() {
    const modal = document.getElementById('modal-overlay');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }
}

/**
 * Setup modal functionality
 */
function setupModal() {
    const modal = document.getElementById('modal-overlay');
    const closeBtn = document.querySelector('.modal-close');
    
    if (closeBtn) {
        closeBtn.addEventListener('click', closeModal);
    }
    
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });
    }
    
    // ESC key to close modal
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal && modal.style.display !== 'none') {
            closeModal();
        }
    });
}

/**
 * Show modal
 */
function showModal(title, content) {
    const modal = document.getElementById('modal-overlay');
    const modalTitle = document.getElementById('modal-title');
    const modalBody = document.getElementById('modal-body');
    
    if (modal && modalTitle && modalBody) {
        modalTitle.textContent = title;
        modalBody.innerHTML = content;
        modal.style.display = 'flex';
        
        // Focus first input in modal
        const firstInput = modalBody.querySelector('input, select, textarea');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
    }
}

/**
 * Close modal
 */
function closeModal() {
    const modal = document.getElementById('modal-overlay');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * Setup auto-refresh for real-time updates
 */
function setupAutoRefresh() {
    // Auto-refresh every 5 minutes for dashboard
    if (window.location.search.includes('page=dashboard') || 
        window.location.pathname.endsWith('/store-admin/')) {
        setInterval(() => {
            refreshDashboard();
        }, 300000); // 5 minutes
    }
}

/**
 * Refresh dashboard statistics
 */
function refreshDashboard() {
    fetch('/store-admin/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'ajax=1&action=get_dashboard_stats'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateDashboardStats(data.stats);
        }
    })
    .catch(error => console.error('Error refreshing dashboard:', error));
}

/**
 * Update dashboard statistics
 */
function updateDashboardStats(stats) {
    // Update stat numbers
    Object.keys(stats).forEach(key => {
        const element = document.querySelector(`[data-stat="${key}"]`);
        if (element) {
            element.textContent = stats[key];
        }
    });
}

/**
 * Add new item
 */
function addItem(type) {
    currentEditId = null;
    loadItemForm(type, null);
}

/**
 * Edit item
 */
function editItem(type, id) {
    currentEditId = id;
    loadItemForm(type, id);
}

/**
 * Get current page type from URL
 */
function getCurrentPageType() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('page') || 'dashboard';
}



/**
 * Load item form
 */
function loadItemForm(type, id) {
    const action = id ? 'edit' : 'add';
    const title = `${action === 'add' ? 'Add' : 'Edit'} ${type.charAt(0).toUpperCase() + type.slice(0, -1)}`;

    // Add cache busting timestamp
    const timestamp = Date.now();

    fetch('/store-admin/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        },
        body: `ajax=1&action=get_form&type=${type}&id=${id || ''}&_t=${timestamp}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showModal(title, data.form);
            setupModalForm();
        } else {
            alert('Error loading form: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error loading form:', error);
        alert('Error loading form. Please try again.');
    });
}

/**
 * Setup modal form
 */
function setupModalForm() {
    const form = document.querySelector('#modal-body form');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            submitModalForm(this);
        });
    }
}

/**
 * Submit modal form
 */
function submitModalForm(form) {
    const formData = new FormData(form);
    formData.append('ajax', '1');

    // Determine the correct action based on current page
    const currentPage = new URLSearchParams(window.location.search).get('page') || 'dashboard';
    const action = formData.get('action');

    if (action === 'save') {
        // Convert generic 'save' action to specific AJAX action
        const actionMap = {
            'categories': 'save_category',
            'services': 'save_service',
            'employees': 'save_employee',
            'customers': 'save_customer',
            'reservations': 'save_reservation'
        };

        if (actionMap[currentPage]) {
            formData.set('action', actionMap[currentPage]);
        }
    }

    fetch('/store-admin/', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal();
            showSuccess(data.message || 'Item saved successfully');
            setTimeout(() => {
                // Force a hard refresh to avoid caching issues
                location.reload(true);
            }, 1000);
        } else {
            showError(data.error || 'Error saving item');
        }
    })
    .catch(error => {
        console.error('Error saving item:', error);
        showError('Error saving item. Please try again.');
    });
}

/**
 * Delete item
 */
function deleteItem(table, id) {
    if (!confirm('Are you sure you want to delete this item?')) {
        return;
    }
    
    fetch('/store-admin/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `ajax=1&action=delete_item&table=${table}&id=${id}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('Item deleted successfully');
            setTimeout(() => {
                // Force a hard refresh to avoid caching issues
                location.reload(true);
            }, 1000);
        } else {
            showError(data.error || 'Error deleting item');
        }
    })
    .catch(error => {
        console.error('Error deleting item:', error);
        showError('Error deleting item. Please try again.');
    });
}

/**
 * Get current page type
 */
function getCurrentPageType() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('page') || 'dashboard';
}

/**
 * Show success message
 */
function showSuccess(message) {
    showNotification(message, 'success');
}

/**
 * Show error message
 */
function showError(message) {
    showNotification(message, 'error');
}

/**
 * Show notification
 */
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add notification styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? 'var(--success-color)' : 'var(--danger-color)'};
        color: white;
        padding: 15px 20px;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 3000;
        display: flex;
        align-items: center;
        gap: 10px;
        max-width: 400px;
        animation: slideIn 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Format currency
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('el-GR', {
        style: 'currency',
        currency: 'EUR'
    }).format(amount);
}

/**
 * Format date
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('el-GR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

/**
 * Format time
 */
function formatTime(timeString) {
    const [hours, minutes] = timeString.split(':');
    return `${hours}:${minutes}`;
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Throttle function
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Get services by category (for reservations)
 */
function getServicesByCategory(categoryId) {
    const serviceSelect = document.getElementById('service_id');
    
    if (!serviceSelect) return;
    
    fetch('/store-admin/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `ajax=1&action=get_services&category_id=${categoryId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            serviceSelect.innerHTML = '<option value="">Select Service</option>';
            data.services.forEach(service => {
                const option = document.createElement('option');
                option.value = service.id;
                option.textContent = `${service.name} (${formatCurrency(service.price)})`;
                serviceSelect.appendChild(option);
            });
        }
    })
    .catch(error => console.error('Error loading services:', error));
}

/**
 * Get available time slots
 */
function getAvailableSlots(date, serviceId, employeeId = '') {
    const slotsContainer = document.getElementById('available-slots');
    
    if (!slotsContainer) return;
    
    slotsContainer.innerHTML = '<div class="loading">Loading available slots...</div>';
    
    fetch('/store-admin/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `ajax=1&action=get_available_slots&date=${date}&service_id=${serviceId}&employee_id=${employeeId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.slots.length === 0) {
                slotsContainer.innerHTML = '<div class="no-slots">No available slots for this date</div>';
            } else {
                slotsContainer.innerHTML = data.slots.map(slot => 
                    `<button type="button" class="slot-btn" onclick="selectSlot('${slot}')">${slot}</button>`
                ).join('');
            }
        } else {
            slotsContainer.innerHTML = '<div class="error">Error loading slots</div>';
        }
    })
    .catch(error => {
        console.error('Error loading slots:', error);
        slotsContainer.innerHTML = '<div class="error">Error loading slots</div>';
    });
}

/**
 * Select time slot
 */
function selectSlot(time) {
    const timeInput = document.getElementById('start_time');
    const slotButtons = document.querySelectorAll('.slot-btn');
    
    if (timeInput) {
        timeInput.value = time;
    }
    
    slotButtons.forEach(btn => btn.classList.remove('selected'));
    event.target.classList.add('selected');
}

/**
 * Copy text to clipboard
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showSuccess('Copied to clipboard');
    }).catch(err => {
        console.error('Failed to copy:', err);
        showError('Failed to copy to clipboard');
    });
}

/**
 * Export data
 */
function exportData(type, format = 'csv') {
    const params = new URLSearchParams(window.location.search);
    params.set('action', 'export');
    params.set('format', format);
    
    const url = `/store-admin/?${params.toString()}`;
    window.open(url, '_blank');
}

/**
 * Print current page
 */
function printPage() {
    window.print();
}

/**
 * Toggle day hours visibility for employee working hours
 */
function toggleDayHours(day) {
    const checkbox = document.querySelector(`input[name="working_days[]"][value="${day}"]`);
    const timesDiv = document.getElementById(`${day}_times`);

    if (checkbox && timesDiv) {
        if (checkbox.checked) {
            timesDiv.style.display = 'flex';
        } else {
            timesDiv.style.display = 'none';
        }
    }
}

/**
 * Make toggleDayHours globally available
 */
window.toggleDayHours = toggleDayHours;

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .notification {
        animation: slideIn 0.3s ease-out;
    }
    
    .notification-close {
        background: none;
        border: none;
        color: inherit;
        cursor: pointer;
        font-size: 16px;
        padding: 0;
        margin-left: 10px;
    }
    
    .loading {
        text-align: center;
        padding: 20px;
        color: var(--text-muted);
    }
    
    .no-slots, .error {
        text-align: center;
        padding: 20px;
        color: var(--text-muted);
    }
    
    .error {
        color: var(--danger-color);
    }
    
    .slot-btn {
        margin: 5px;
        padding: 8px 15px;
        border: 1px solid var(--border-color);
        background: white;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
    }
    
    .slot-btn:hover {
        background: var(--light-color);
        border-color: var(--primary-color);
    }
    
    .slot-btn.selected {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }
`;
document.head.appendChild(style);

// Special Days functionality
function initializeSpecialDays() {
    // Handle schedule type radio buttons
    const scheduleTypeRadios = document.querySelectorAll('input[name="schedule_type"]');
    const customHoursDiv = document.getElementById('custom-hours');

    if (scheduleTypeRadios.length > 0 && customHoursDiv) {
        scheduleTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'general_hours') {
                    customHoursDiv.style.display = 'block';
                } else {
                    customHoursDiv.style.display = 'none';
                }
            });
        });

        // Initialize based on current selection
        const selectedRadio = document.querySelector('input[name="schedule_type"]:checked');
        if (selectedRadio && selectedRadio.value === 'general_hours') {
            customHoursDiv.style.display = 'block';
        }
    }
}

// Function to show special day form
function showSpecialDayForm() {
    const formHtml = `
        <form method="POST" action="">
            <input type="hidden" name="action" value="save_special_day">

            <div class="form-group">
                <label for="special_date">Date</label>
                <input type="date" id="special_date" name="special_date" required>
            </div>

            <div class="form-group">
                <label for="special_note">Note (optional)</label>
                <input type="text" id="special_note" name="special_note" placeholder="Holiday, Special Event, etc.">
            </div>

            <div class="form-group">
                <label>Schedule Type</label>
                <div class="radio-group">
                    <label><input type="radio" name="schedule_type" value="closed" checked> Closed All Day</label>
                    <label><input type="radio" name="schedule_type" value="general_hours"> Custom Hours</label>
                </div>
            </div>

            <div id="custom-hours" style="display: none;">
                <div class="form-group">
                    <label>Hours</label>
                    <div class="time-period">
                        <input type="time" name="general_start[]" value="09:00">
                        <span>to</span>
                        <input type="time" name="general_end[]" value="17:00">
                    </div>
                </div>
            </div>

            <button type="submit" class="btn btn-primary">Save Special Day</button>
        </form>
    `;

    // Create modal or replace content
    const container = document.querySelector('.special-days-form-container');
    if (container) {
        container.innerHTML = formHtml;
        // Re-initialize the form functionality
        initializeSpecialDays();
    }
}

// Initialize special days when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeSpecialDays();
});
