<?php

/**
 * Database Cleanup and Management Tools
 * Clean, backup, and manage tenant databases
 */

require_once __DIR__ . '/config.php';

// Require system authentication
requireSystemAuth();

$message = '';
$messageType = 'info';

// Get all tenants
$tenants = [];
try {
    $tenants = Database::master()->fetchAll("SELECT * FROM tenants ORDER BY business_name");
} catch (Exception $e) {
    // System database might not exist yet - that's ok for factory reset
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $tenantId = $_POST['tenant_id'] ?? '';
    
    try {
        switch ($action) {
            case 'clean_reservations':
                if ($tenantId) {
                    // Get tenant subdomain from ID
                    $tenant = Database::master()->fetchRow("SELECT subdomain FROM tenants WHERE id = :id", [':id' => $tenantId]);
                    if ($tenant) {
                        $tenantDb = Database::tenant($tenant['subdomain']);
                        $result = $tenantDb->query("DELETE FROM reservations");
                        $message = "All reservations deleted for selected tenant";
                        $messageType = 'success';
                    } else {
                        $message = "Tenant not found";
                        $messageType = 'error';
                    }
                } else {
                    $message = "Please select a tenant";
                    $messageType = 'error';
                }
                break;

            case 'clean_customers':
                if ($tenantId) {
                    // Get tenant subdomain from ID
                    $tenant = Database::master()->fetchRow("SELECT subdomain FROM tenants WHERE id = :id", [':id' => $tenantId]);
                    if ($tenant) {
                        $tenantDb = Database::tenant($tenant['subdomain']);
                        $tenantDb->query("DELETE FROM reservations");
                        $tenantDb->query("DELETE FROM customers");
                        $message = "All customers and their reservations deleted";
                        $messageType = 'success';
                    } else {
                        $message = "Tenant not found";
                        $messageType = 'error';
                    }
                } else {
                    $message = "Please select a tenant";
                    $messageType = 'error';
                }
                break;
                
            case 'clean_services':
                if ($tenantId) {
                    // Get tenant subdomain from ID
                    $tenant = Database::master()->fetchRow("SELECT subdomain FROM tenants WHERE id = :id", [':id' => $tenantId]);
                    if ($tenant) {
                        $tenantDb = Database::tenant($tenant['subdomain']);
                        $tenantDb->query("DELETE FROM reservations");
                        $tenantDb->query("DELETE FROM employee_services");
                        $tenantDb->query("DELETE FROM services");
                        $tenantDb->query("DELETE FROM categories");
                        $message = "All services, categories and related data deleted";
                        $messageType = 'success';
                    } else {
                        $message = "Tenant not found";
                        $messageType = 'error';
                    }
                } else {
                    $message = "Please select a tenant";
                    $messageType = 'error';
                }
                break;

            case 'clean_employees':
                if ($tenantId) {
                    // Get tenant subdomain from ID
                    $tenant = Database::master()->fetchRow("SELECT subdomain FROM tenants WHERE id = :id", [':id' => $tenantId]);
                    if ($tenant) {
                        $tenantDb = Database::tenant($tenant['subdomain']);
                        $tenantDb->query("UPDATE reservations SET employee_id = NULL");
                        $tenantDb->query("DELETE FROM employee_services");
                        $tenantDb->query("DELETE FROM employees");
                        $message = "All employees deleted (reservations kept but unassigned)";
                        $messageType = 'success';
                    } else {
                        $message = "Tenant not found";
                        $messageType = 'error';
                    }
                } else {
                    $message = "Please select a tenant";
                    $messageType = 'error';
                }
                break;
                
            case 'clean_all_data':
                if ($tenantId) {
                    // Get tenant subdomain from ID
                    $tenant = Database::master()->fetchRow("SELECT subdomain FROM tenants WHERE id = :id", [':id' => $tenantId]);
                    if ($tenant) {
                        $tenantDb = Database::tenant($tenant['subdomain']);
                        $tenantDb->query("DELETE FROM reservations");
                        $tenantDb->query("DELETE FROM customers");
                        $tenantDb->query("DELETE FROM employee_services");
                        $tenantDb->query("DELETE FROM employees");
                        $tenantDb->query("DELETE FROM services");
                        $tenantDb->query("DELETE FROM categories");

                        // Delete verification codes if table exists
                        try {
                            $tenantDb->query("DELETE FROM verification_codes");
                        } catch (Exception $e) {
                            // Table might not exist in older databases - ignore
                        }

                        // Delete texts if table has category column (new schema)
                        try {
                            $tenantDb->query("DELETE FROM texts WHERE category NOT IN ('ui', 'common')");
                        } catch (Exception $e) {
                            // Table might have old schema without category column - delete all non-system texts
                            try {
                                $tenantDb->query("DELETE FROM texts WHERE text_key NOT LIKE 'system_%' AND text_key NOT LIKE 'ui_%'");
                            } catch (Exception $e2) {
                                // Even older schema - skip text deletion
                            }
                        }
                        $message = "All business data deleted (settings preserved)";
                        $messageType = 'success';
                    } else {
                        $message = "Tenant not found";
                        $messageType = 'error';
                    }
                } else {
                    $message = "Please select a tenant";
                    $messageType = 'error';
                }
                break;

            case 'reset_tenant':
                if ($tenantId) {
                    // Get tenant subdomain from ID
                    $tenant = Database::master()->fetchRow("SELECT subdomain FROM tenants WHERE id = :id", [':id' => $tenantId]);
                    if ($tenant) {
                        // Delete the database file
                        $dbPath = Config::getTenantDbPath($tenant['subdomain']);
                        if (file_exists($dbPath)) {
                            unlink($dbPath);
                        }

                        // Recreate the tenant database (will trigger schema initialization)
                        Database::tenant($tenant['subdomain']);

                        $message = "Tenant database completely reset to default state";
                        $messageType = 'success';
                    } else {
                        $message = "Tenant not found";
                        $messageType = 'error';
                    }
                } else {
                    $message = "Please select a tenant";
                    $messageType = 'error';
                }
                break;
                
            case 'backup_tenant':
                if ($tenantId) {
                    $tenant = Database::master()->fetchRow("SELECT * FROM tenants WHERE id = :id", [':id' => $tenantId]);
                    if ($tenant && backupDatabase($tenant['subdomain'])) {
                        $message = "Database backup created successfully for " . htmlspecialchars($tenant['business_name']);
                        $messageType = 'success';
                    } else {
                        $message = "Failed to create backup";
                        $messageType = 'error';
                    }
                } else {
                    $message = "Please select a tenant";
                    $messageType = 'error';
                }
                break;
                
            case 'cleanup_logs':
                $cleaned = cleanupLogs();
                $message = "Cleaned up $cleaned old log files";
                $messageType = 'success';
                break;
                
            case 'vacuum_databases':
                $vacuumed = 0;
                // Vacuum master database
                Database::master()->exec("VACUUM");
                $vacuumed++;

                // Vacuum all tenant databases
                foreach ($tenants as $tenant) {
                    try {
                        Database::tenant($tenant['subdomain'])->exec("VACUUM");
                        $vacuumed++;
                    } catch (Exception $e) {
                        // Skip if database is corrupted
                    }
                }

                $message = "Vacuumed $vacuumed databases (optimized storage)";
                $messageType = 'success';
                break;

            case 'factory_reset':
                if (isset($_POST['confirm_factory_reset']) && $_POST['confirm_factory_reset'] === 'YES_DELETE_EVERYTHING') {
                    try {
                        // Get all tenants before deletion
                        $allTenants = [];
                        try {
                            $allTenants = Database::master()->fetchAll("SELECT subdomain FROM tenants");
                        } catch (Exception $e) {
                            // System database might not exist yet - that's ok
                        }

                        // Close all database connections
                        try {
                            Database::closeAllInstances();
                        } catch (Exception $e) {
                            // Ignore connection close errors
                        }

                        // Delete all tenant database files
                        $dataDir = __DIR__ . '/../data';
                        if (is_dir($dataDir . '/tenants')) {
                            $files = glob($dataDir . '/tenants/*.db');
                            foreach ($files as $file) {
                                if (file_exists($file)) {
                                    $attempts = 0;
                                    while ($attempts < 5) {
                                        if (@unlink($file)) {
                                            break;
                                        }
                                        $attempts++;
                                        usleep(100000); // Wait 100ms
                                    }
                                }
                            }
                        }

                        // Delete system database file
                        $systemDbPath = $dataDir . '/system/system.db';
                        if (file_exists($systemDbPath)) {
                            $attempts = 0;
                            while ($attempts < 5) {
                                if (@unlink($systemDbPath)) {
                                    break;
                                }
                                $attempts++;
                                usleep(100000); // Wait 100ms
                            }
                        }

                        // Recreate system database (will auto-initialize schema)
                        $systemDb = Database::master();

                        // Create default demo tenant
                        $result = TenantManager::createTenant([
                            'subdomain' => 'demo',
                            'business_name' => 'Demo Business',
                            'owner_name' => 'Demo Owner',
                            'owner_email' => '<EMAIL>',
                            'admin_username' => 'admin',
                            'admin_password' => 'admin123'
                        ]);

                        if ($result['success']) {
                            // Ensure demo tenant database is properly created
                            try {
                                $demoDb = Database::getInstance('demo');
                                $message = "🎉 Factory reset completed! Default demo tenant created. Login: admin / admin123";
                                $messageType = 'success';
                            } catch (Exception $e) {
                                $message = "Factory reset completed but demo tenant database creation failed: " . $e->getMessage();
                                $messageType = 'warning';
                            }
                        } else {
                            $message = "Factory reset completed but failed to create demo tenant: " . $result['error'];
                            $messageType = 'warning';
                        }

                        // Refresh tenants list
                        $tenants = Database::master()->fetchAll("SELECT * FROM tenants ORDER BY business_name");

                    } catch (Exception $e) {
                        $message = "Factory reset failed: " . $e->getMessage();
                        $messageType = 'error';
                        error_log("Factory reset error: " . $e->getMessage());
                    }
                } else {
                    $message = "Factory reset cancelled - confirmation text was incorrect";
                    $messageType = 'error';
                }
                break;
                
            default:
                $message = "Unknown action";
                $messageType = 'error';
        }
    } catch (Exception $e) {
        $message = "Error: " . $e->getMessage();
        $messageType = 'error';
    }
}

// Get database statistics
$dbStats = [];
foreach ($tenants as $tenant) {
    try {
        $tenantDb = Database::tenant($tenant['subdomain']);
        $dbPath = Config::getTenantDbPath($tenant['subdomain']);

        $stats = [
            'size' => getDatabaseSize($dbPath),
            'customers' => $tenantDb->fetchRow("SELECT COUNT(*) as count FROM customers")['count'] ?? 0,
            'reservations' => $tenantDb->fetchRow("SELECT COUNT(*) as count FROM reservations")['count'] ?? 0,
            'services' => $tenantDb->fetchRow("SELECT COUNT(*) as count FROM services")['count'] ?? 0,
            'employees' => $tenantDb->fetchRow("SELECT COUNT(*) as count FROM employees")['count'] ?? 0,
            'categories' => $tenantDb->fetchRow("SELECT COUNT(*) as count FROM categories")['count'] ?? 0,
        ];

        $dbStats[$tenant['id']] = $stats;
    } catch (Exception $e) {
        $dbStats[$tenant['id']] = ['error' => $e->getMessage()];
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= SYSTEM_NAME ?> - Database Tools</title>
    <link rel="stylesheet" href="assets/system.css">
</head>
<body>
    <div class="layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1><?= SYSTEM_NAME ?></h1>
                <button class="sidebar-toggle">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <line x1="3" y1="12" x2="21" y2="12"></line>
                        <line x1="3" y1="18" x2="21" y2="18"></line>
                    </svg>
                </button>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <a href="index.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="7" height="7"></rect>
                            <rect x="14" y="3" width="7" height="7"></rect>
                            <rect x="14" y="14" width="7" height="7"></rect>
                            <rect x="3" y="14" width="7" height="7"></rect>
                        </svg>
                        <span class="nav-text">Dashboard</span>
                    </a>
                    <a href="tenant_manager.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                        <span class="nav-text">Tenant Manager</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Management</div>
                    <a href="create_tenant.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="16"></line>
                            <line x1="8" y1="12" x2="16" y2="12"></line>
                        </svg>
                        <span class="nav-text">Create Tenant</span>
                    </a>
                    <a href="dummy_data.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10,9 9,9 8,9"></polyline>
                        </svg>
                        <span class="nav-text">Dummy Data</span>
                    </a>
                    <a href="clean_db.php" class="nav-link active">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 7h16"></path>
                            <path d="M10 11v6"></path>
                            <path d="M14 11v6"></path>
                            <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2l1-12"></path>
                            <path d="M9 7V4a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3"></path>
                        </svg>
                        <span class="nav-text">Database Tools</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <a href="?logout=1" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                            <polyline points="16,17 21,12 16,7"></polyline>
                            <line x1="21" y1="12" x2="9" y2="12"></line>
                        </svg>
                        <span class="nav-text">Logout</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <div class="main-content">
            <header class="header">
                <div class="header-left">
                    <h1 class="header-title">Database Tools</h1>
                    <div class="breadcrumb">
                        <span>System</span>
                        <span class="breadcrumb-separator">/</span>
                        <span>Management</span>
                        <span class="breadcrumb-separator">/</span>
                        <span>Database Tools</span>
                    </div>
                </div>
                <div class="header-right">
                    <p class="page-subtitle">Clean, backup, and manage tenant databases</p>
                </div>
            </header>

            <main class="main">
                <div class="container">

            <?php if ($message): ?>
                <div class="alert alert-<?= $messageType ?>">
                    <?= $message ?>
                </div>
            <?php endif; ?>

            <div class="tools-grid">
                <!-- Tenant-Specific Tools -->
                <div class="tool-section">
                    <h2>Tenant Database Tools</h2>
                    
                    <form method="post" class="tool-form">
                        <div class="form-group">
                            <label for="tenant_id">Select Tenant</label>
                            <select id="tenant_id" name="tenant_id" required>
                                <option value="">Choose a tenant...</option>
                                <?php foreach ($tenants as $tenant): ?>
                                    <option value="<?= $tenant['id'] ?>">
                                        <?= htmlspecialchars($tenant['business_name']) ?> (<?= htmlspecialchars($tenant['subdomain']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="tool-buttons">
                            <button type="submit" name="action" value="clean_reservations" 
                                    class="btn btn-warning" onclick="return confirm('Delete all reservations for this tenant?')">
                                Clean Reservations
                            </button>
                            
                            <button type="submit" name="action" value="clean_customers" 
                                    class="btn btn-warning" onclick="return confirm('Delete all customers and their reservations?')">
                                Clean Customers
                            </button>
                            
                            <button type="submit" name="action" value="clean_services" 
                                    class="btn btn-warning" onclick="return confirm('Delete all services and categories?')">
                                Clean Services
                            </button>
                            
                            <button type="submit" name="action" value="clean_employees" 
                                    class="btn btn-warning" onclick="return confirm('Delete all employees?')">
                                Clean Employees
                            </button>
                            
                            <button type="submit" name="action" value="clean_all_data" 
                                    class="btn btn-danger" onclick="return confirm('Delete ALL business data? This cannot be undone!')">
                                Clean All Data
                            </button>
                            
                            <button type="submit" name="action" value="reset_tenant" 
                                    class="btn btn-danger" onclick="return confirm('RESET tenant to default state? This will delete everything!')">
                                Reset Tenant
                            </button>
                        </div>
                    </form>
                </div>

                <!-- System Tools -->
                <div class="tool-section">
                    <h2>System Tools</h2>

                    <form method="post" class="tool-form">
                        <div class="tool-buttons">
                            <button type="submit" name="action" value="backup_tenant"
                                    class="btn btn-info" onclick="return getTenantForBackup()">
                                Backup Tenant
                            </button>

                            <button type="submit" name="action" value="cleanup_logs"
                                    class="btn btn-secondary" onclick="return confirm('Clean up old log files?')">
                                Cleanup Logs
                            </button>

                            <button type="submit" name="action" value="vacuum_databases"
                                    class="btn btn-info" onclick="return confirm('Optimize all databases? This may take a moment.')">
                                Vacuum Databases
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Factory Reset -->
                <div class="tool-section">
                    <h2>🚨 Development Tools</h2>
                    <div class="alert alert-warning">
                        <strong>Warning:</strong> These tools are for development only and will permanently delete ALL data!
                    </div>

                    <form method="post" class="tool-form" onsubmit="return confirmFactoryReset()">
                        <div class="form-group">
                            <label for="confirm_factory_reset">Type "YES_DELETE_EVERYTHING" to confirm factory reset:</label>
                            <input type="text" id="confirm_factory_reset" name="confirm_factory_reset"
                                   placeholder="Type confirmation text here..." class="form-control">
                        </div>

                        <div class="tool-buttons">
                            <button type="submit" name="action" value="factory_reset"
                                    class="btn btn-danger">
                                🏭 Factory Reset System
                            </button>
                        </div>

                        <div class="help-text">
                            <small>This will:</small>
                            <ul>
                                <li>Delete ALL tenant databases</li>
                                <li>Delete system database</li>
                                <li>Recreate fresh system</li>
                                <li>Create default "demo" tenant</li>
                                <li>Set admin credentials: admin / admin123</li>
                            </ul>
                        </div>
                    </form>
                </div>

                <!-- Database Statistics -->
                <div class="tool-section">
                    <h2>Database Statistics</h2>
                    
                    <div class="db-stats">
                        <?php if (empty($tenants)): ?>
                            <p>No tenants found.</p>
                        <?php else: ?>
                            <table class="stats-table">
                                <thead>
                                    <tr>
                                        <th>Tenant</th>
                                        <th>Size</th>
                                        <th>Customers</th>
                                        <th>Reservations</th>
                                        <th>Services</th>
                                        <th>Employees</th>
                                        <th>Categories</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($tenants as $tenant): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($tenant['business_name']) ?></strong><br>
                                                <small><?= htmlspecialchars($tenant['subdomain']) ?></small>
                                            </td>
                                            <?php if (isset($dbStats[$tenant['id']]['error'])): ?>
                                                <td colspan="6" class="text-danger">
                                                    Error: <?= htmlspecialchars($dbStats[$tenant['id']]['error']) ?>
                                                </td>
                                            <?php else: ?>
                                                <td><?= formatFileSize($dbStats[$tenant['id']]['size']) ?></td>
                                                <td><?= $dbStats[$tenant['id']]['customers'] ?></td>
                                                <td><?= $dbStats[$tenant['id']]['reservations'] ?></td>
                                                <td><?= $dbStats[$tenant['id']]['services'] ?></td>
                                                <td><?= $dbStats[$tenant['id']]['employees'] ?></td>
                                                <td><?= $dbStats[$tenant['id']]['categories'] ?></td>
                                            <?php endif; ?>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="help-section">
                <h2>Tool Descriptions</h2>
                <div class="help-content">
                    <div class="help-item">
                        <h3>Clean Reservations</h3>
                        <p>Removes all booking records while keeping customers, services, and employees.</p>
                    </div>
                    
                    <div class="help-item">
                        <h3>Clean Customers</h3>
                        <p>Removes all customer records and their associated reservations.</p>
                    </div>
                    
                    <div class="help-item">
                        <h3>Clean Services</h3>
                        <p>Removes all services and categories. Also removes related reservations.</p>
                    </div>
                    
                    <div class="help-item">
                        <h3>Clean Employees</h3>
                        <p>Removes all employee records. Existing reservations are kept but unassigned.</p>
                    </div>
                    
                    <div class="help-item">
                        <h3>Clean All Data</h3>
                        <p>Removes all business data but keeps settings and configuration.</p>
                    </div>
                    
                    <div class="help-item">
                        <h3>Reset Tenant</h3>
                        <p>Completely resets the tenant to its initial state. ALL data is lost.</p>
                    </div>
                    
                    <div class="help-item">
                        <h3>Backup Tenant</h3>
                        <p>Creates a backup copy of the tenant's database in the storage/backups folder.</p>
                    </div>
                    
                    <div class="help-item">
                        <h3>Vacuum Databases</h3>
                        <p>Optimizes database storage by removing unused space and rebuilding indexes.</p>
                    </div>

                    <div class="help-item">
                        <h3>🏭 Factory Reset System</h3>
                        <p><strong>Development Only:</strong> Completely resets the entire system to factory defaults. Deletes ALL tenants and data, then creates a fresh "demo" tenant with admin/admin123 credentials. Perfect for starting development from scratch.</p>
                    </div>
                </div>
            </div>
        </div>
    </main>
        </div>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay"></div>

    <script src="assets/system.js"></script>
    <script>
        function getTenantForBackup() {
            const tenantId = document.getElementById('tenant_id').value;
            if (!tenantId) {
                alert('Please select a tenant first');
                return false;
            }

            // Add tenant_id to backup form
            const form = event.target.form;
            let tenantInput = form.querySelector('input[name="tenant_id"]');
            if (!tenantInput) {
                tenantInput = document.createElement('input');
                tenantInput.type = 'hidden';
                tenantInput.name = 'tenant_id';
                form.appendChild(tenantInput);
            }
            tenantInput.value = tenantId;

            return confirm('Create backup for selected tenant?');
        }

        function confirmFactoryReset() {
            const confirmText = document.getElementById('confirm_factory_reset').value;

            if (confirmText !== 'YES_DELETE_EVERYTHING') {
                alert('Please type "YES_DELETE_EVERYTHING" exactly to confirm factory reset');
                return false;
            }

            return confirm(
                '⚠️ FINAL WARNING ⚠️\n\n' +
                'This will PERMANENTLY DELETE:\n' +
                '• ALL tenant databases\n' +
                '• ALL customer data\n' +
                '• ALL bookings\n' +
                '• ALL services\n' +
                '• ALL employees\n' +
                '• EVERYTHING!\n\n' +
                'Are you absolutely sure you want to proceed?'
            );
        }

        // Add tenant selection to system tools
        document.querySelectorAll('.tool-section:nth-child(2) button').forEach(button => {
            if (button.name === 'action' && button.value === 'backup_tenant') {
                button.addEventListener('click', function(e) {
                    const tenantId = document.getElementById('tenant_id').value;
                    if (!tenantId) {
                        e.preventDefault();
                        alert('Please select a tenant first');
                        return;
                    }
                });
            }
        });
    </script>
</body>
</html>
