<?php

/**
 * Test script to verify tenant security fixes
 */

require_once __DIR__ . '/shared/config.php';
require_once __DIR__ . '/shared/database.php';
require_once __DIR__ . '/shared/tenant_manager.php';

echo "Testing Tenant Security Fixes\n";
echo "=============================\n\n";

try {
    // Initialize systems
    Config::init();
    
    echo "1. Testing tenant existence check...\n";
    
    // Test existing tenant
    $exists = TenantManager::tenantExists('demo');
    echo "   Demo tenant exists: " . ($exists ? "YES" : "NO") . "\n";
    
    // Test non-existing tenant
    $exists = TenantManager::tenantExists('nonexistent');
    echo "   Non-existent tenant exists: " . ($exists ? "YES" : "NO") . "\n";
    
    echo "\n2. Testing database access control...\n";
    
    // Test accessing existing tenant database
    try {
        $demoDb = Database::tenantSafe('demo');
        echo "   Demo database access: SUCCESS\n";
    } catch (Exception $e) {
        echo "   Demo database access: FAILED - " . $e->getMessage() . "\n";
    }
    
    // Test accessing non-existing tenant database
    try {
        $fakeDb = Database::tenantSafe('nonexistent');
        echo "   Non-existent database access: ALLOWED (SECURITY ISSUE!)\n";
    } catch (Exception $e) {
        echo "   Non-existent database access: BLOCKED - " . $e->getMessage() . "\n";
    }
    
    echo "\n3. Testing DummyData class...\n";
    
    if (file_exists(__DIR__ . '/shared/DummyData.php')) {
        require_once __DIR__ . '/shared/DummyData.php';
        if (class_exists('DummyData')) {
            $dummyData = new DummyData();
            echo "   DummyData class: LOADED SUCCESSFULLY\n";
        } else {
            echo "   DummyData class: CLASS NOT FOUND\n";
        }
    } else {
        echo "   DummyData file: NOT FOUND\n";
    }
    
    echo "\nAll tests completed!\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
