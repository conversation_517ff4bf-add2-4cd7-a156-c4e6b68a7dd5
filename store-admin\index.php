<?php
/**
 * Store Admin Dashboard
 * Main admin interface for complete store management
 */

session_start();
require_once __DIR__ . '/core/Application.php';
require_once __DIR__ . '/core/Pagination.php';
require_once __DIR__ . '/core/AdminHelpers.php';

Application::init();

// Check authentication
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: /store-admin/login.php');
    exit;
}

$page = $_GET['page'] ?? 'dashboard';
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';

// Handle AJAX requests
if (isset($_POST['ajax'])) {
    require_once __DIR__ . '/controllers/ajax.php';
    exit;
}

// Handle form submissions
if ($_POST && !isset($_POST['ajax'])) {
    $result = handleFormSubmission($_POST, $page);
    if (isset($result['redirect'])) {
        Application::redirect($result['redirect'], $result['message'] ?? '', $result['type'] ?? 'success');
    }
}

/**
 * Handle form submissions
 */
function handleFormSubmission(array $data, string $page): array
{
    $controllerFile = __DIR__ . "/controllers/{$page}.php";
    
    if (file_exists($controllerFile)) {
        require_once $controllerFile;
        $functionName = "handle" . ucfirst($page) . "Form";
        if (function_exists($functionName)) {
            return $functionName($data, Application::getDb());
        }
    }
    
    return ['success' => false, 'error' => 'Controller not found'];
}

$db = Application::getDb();
$flashMessage = Application::getFlashMessage();

// Get dashboard statistics
$stats = [];
if ($page === 'dashboard') {
    $stats = [
        'total_categories' => $db->fetchRow("SELECT COUNT(*) as count FROM categories")['count'],
        'total_services' => $db->fetchRow("SELECT COUNT(*) as count FROM services")['count'],
        'total_employees' => $db->fetchRow("SELECT COUNT(*) as count FROM employees")['count'],
        'total_customers' => $db->fetchRow("SELECT COUNT(*) as count FROM customers")['count'],
        'total_reservations' => $db->fetchRow("SELECT COUNT(*) as count FROM reservations")['count'],
        'recent_reservations' => $db->fetchAll("
            SELECT r.*, c.name as customer_name, s.name as service_name, e.name as employee_name
            FROM reservations r
            LEFT JOIN customers c ON r.customer_id = c.id
            LEFT JOIN services s ON r.service_id = s.id
            LEFT JOIN employees e ON r.employee_id = e.id
            ORDER BY r.created_at DESC LIMIT 5
        ")
    ];
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo ucfirst($page); ?> - Store Admin</title>
    <link rel="stylesheet" href="/store-admin/assets/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <!-- Enhanced Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1><i class="fas fa-store"></i> Store Admin</h1>
            </div>

            <div class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <a href="/store-admin/" class="nav-link <?php echo $page === 'dashboard' ? 'active' : ''; ?>">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                    <a href="/store-admin/?page=customers" class="nav-link <?php echo $page === 'customers' ? 'active' : ''; ?>">
                        <i class="fas fa-users nav-icon"></i>
                        <span class="nav-text">Customers</span>
                        <?php
                        // Get customer count for badge
                        try {
                            $customerCount = $db->fetchColumn("SELECT COUNT(*) FROM customers");
                            if ($customerCount > 0): ?>
                                <span class="nav-badge"><?php echo $customerCount; ?></span>
                            <?php endif;
                        } catch (Exception $e) {
                            // Ignore errors for badge count
                        }
                        ?>
                    </a>
                    <a href="/store-admin/?page=services" class="nav-link <?php echo $page === 'services' ? 'active' : ''; ?>">
                        <i class="fas fa-cut nav-icon"></i>
                        <span class="nav-text">Services</span>
                    </a>
                    <a href="/store-admin/?page=employees" class="nav-link <?php echo $page === 'employees' ? 'active' : ''; ?>">
                        <i class="fas fa-user-tie nav-icon"></i>
                        <span class="nav-text">Employees</span>
                    </a>
                    <a href="/store-admin/?page=categories" class="nav-link <?php echo $page === 'categories' ? 'active' : ''; ?>">
                        <i class="fas fa-tags nav-icon"></i>
                        <span class="nav-text">Categories</span>
                    </a>
                    <a href="/store-admin/?page=reservations" class="nav-link <?php echo $page === 'reservations' ? 'active' : ''; ?>">
                        <i class="fas fa-calendar nav-icon"></i>
                        <span class="nav-text">Reservations</span>
                        <?php
                        // Get pending reservations count for badge
                        try {
                            $pendingCount = $db->fetchColumn("SELECT COUNT(*) FROM reservations WHERE status = 'pending' OR (status = 'confirmed' AND date = CURDATE())");
                            if ($pendingCount > 0): ?>
                                <span class="nav-badge"><?php echo $pendingCount; ?></span>
                            <?php endif;
                        } catch (Exception $e) {
                            // Ignore errors for badge count
                        }
                        ?>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Management</div>
                    <a href="/store-admin/?page=settings" class="nav-link <?php echo $page === 'settings' ? 'active' : ''; ?>">
                        <i class="fas fa-cog nav-icon"></i>
                        <span class="nav-text">Settings</span>
                    </a>
                    <a href="/store-admin/?page=texts" class="nav-link <?php echo $page === 'texts' ? 'active' : ''; ?>">
                        <i class="fas fa-language nav-icon"></i>
                        <span class="nav-text">Texts</span>
                    </a>
                </div>
            </div>

            <div class="sidebar-footer">
                <a href="/store-admin/logout.php" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </nav>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay"></div>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="admin-header">
                <h1><?php echo ucfirst($page); ?></h1>
                <div class="admin-user">
                    <span>Welcome, Admin</span>
                    <a href="/store-admin/logout.php" class="btn btn-secondary">Logout</a>
                </div>
            </header>

            <!-- Flash Messages -->
            <?php if (!empty($flashMessage['message'])): ?>
                <div class="flash-message flash-<?php echo $flashMessage['type']; ?>">
                    <?php echo htmlspecialchars($flashMessage['message']); ?>
                </div>
            <?php endif; ?>

            <!-- Page Content -->
            <div class="page-content">
                <?php
                $viewFile = __DIR__ . "/views/{$page}.php";
                if (file_exists($viewFile)) {
                    require_once $viewFile;
                } else {
                    echo "<p>Page not found: {$page}</p>";
                }
                ?>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div id="modal-overlay" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Modal Title</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Modal content will be loaded here -->
            </div>
        </div>
    </div>

    <script src="/store-admin/assets/admin.js"></script>
</body>
</html>
